import React, { useState, useEffect } from 'react';
import { gradesAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import './Grades.css';

interface Grade {
  id: string;
  submissionId: string;
  studentName: string;
  studentId: string;
  scheduleTitle: string;
  labName: string;
  className: string;
  submissionType: 'file' | 'text' | 'mixed';
  submittedAt: string;
  files: string[];
  textContent?: string;
  score?: number;
  maxScore: number;
  feedback?: string;
  gradedAt?: string;
  gradedBy?: string;
  status: 'pending' | 'graded' | 'reviewed';
  groupName?: string;
}

interface GradingModalProps {
  grade: Grade;
  onClose: () => void;
  onSave: (gradeData: { score: number; feedback: string }) => void;
}

const GradingModal: React.FC<GradingModalProps> = ({ grade, onClose, onSave }) => {
  const [score, setScore] = useState(grade.score || 0);
  const [feedback, setFeedback] = useState(grade.feedback || '');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave({ score, feedback });
      onClose();
    } catch (error) {
      console.error('Error saving grade:', error);
    } finally {
      setSaving(false);
    }
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return '#27ae60';
    if (percentage >= 80) return '#2ecc71';
    if (percentage >= 70) return '#f39c12';
    if (percentage >= 60) return '#e67e22';
    return '#e74c3c';
  };

  return (
    <div className="modal-overlay">
      <div className="grading-modal">
        <div className="modal-header">
          <h2>Grade Submission</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          <div className="submission-info">
            <h3>{grade.scheduleTitle}</h3>
            <div className="info-grid">
              <div className="info-item">
                <label>Student:</label>
                <span>{grade.studentName} ({grade.studentId})</span>
              </div>
              <div className="info-item">
                <label>Lab:</label>
                <span>{grade.labName}</span>
              </div>
              <div className="info-item">
                <label>Class:</label>
                <span>{grade.className}</span>
              </div>
              {grade.groupName && (
                <div className="info-item">
                  <label>Group:</label>
                  <span>{grade.groupName}</span>
                </div>
              )}
              <div className="info-item">
                <label>Submitted:</label>
                <span>{new Date(grade.submittedAt).toLocaleString()}</span>
              </div>
            </div>
          </div>

          {grade.files.length > 0 && (
            <div className="submission-files">
              <h4>Submitted Files:</h4>
              <div className="file-list">
                {grade.files.map((file, index) => (
                  <div key={index} className="file-item">
                    <span className="file-name">{file}</span>
                    <button className="btn btn-sm">Download</button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {grade.textContent && (
            <div className="submission-text">
              <h4>Text Submission:</h4>
              <div className="text-content">
                {grade.textContent}
              </div>
            </div>
          )}

          <div className="grading-section">
            <div className="score-input">
              <label htmlFor="score">Score:</label>
              <div className="score-controls">
                <input
                  type="number"
                  id="score"
                  min="0"
                  max={grade.maxScore}
                  value={score}
                  onChange={(e) => setScore(Number(e.target.value))}
                  className="score-field"
                />
                <span className="max-score">/ {grade.maxScore}</span>
                <div
                  className="score-percentage"
                  style={{ color: getScoreColor(score, grade.maxScore) }}
                >
                  {Math.round((score / grade.maxScore) * 100)}%
                </div>
              </div>
            </div>

            <div className="feedback-input">
              <label htmlFor="feedback">Feedback:</label>
              <textarea
                id="feedback"
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                placeholder="Provide detailed feedback for the student..."
                rows={6}
                className="feedback-field"
              />
            </div>
          </div>
        </div>

        <div className="modal-actions">
          <button className="btn btn-secondary" onClick={onClose}>
            Cancel
          </button>
          <button
            className="btn btn-primary"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Grade'}
          </button>
        </div>
      </div>
    </div>
  );
};

const Grades: React.FC = () => {
  const { user } = useAuth();
  const [grades, setGrades] = useState<Grade[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'graded'>('pending');
  const [selectedGrade, setSelectedGrade] = useState<Grade | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Demo data
  const demoGrades: Grade[] = [
    {
      id: '1',
      submissionId: 'sub1',
      studentName: 'John Doe',
      studentId: '12345678',
      scheduleTitle: 'Web Development Practical',
      labName: 'Lab 1',
      className: '11th Non-Medical A',
      submissionType: 'mixed',
      submittedAt: '2024-01-14T16:30:00Z',
      files: ['index.html', 'style.css', 'script.js'],
      textContent: 'This is my web development project implementing a responsive portfolio website.',
      maxScore: 100,
      status: 'pending',
      groupName: 'Group Alpha'
    },
    {
      id: '2',
      submissionId: 'sub2',
      studentName: 'Jane Smith',
      studentId: '12345679',
      scheduleTitle: 'Database Design Lab',
      labName: 'Lab 2',
      className: '12th Non-Medical A',
      submissionType: 'file',
      submittedAt: '2024-01-10T14:20:00Z',
      files: ['database_schema.sql', 'queries.sql'],
      score: 85,
      maxScore: 100,
      feedback: 'Good work on the database design. Consider adding more indexes for better performance.',
      gradedAt: '2024-01-12T10:00:00Z',
      gradedBy: 'John Smith',
      status: 'graded',
      groupName: 'Group Beta'
    }
  ];

  useEffect(() => {
    const fetchGrades = async () => {
      try {
        setLoading(true);
        const response = await gradesAPI.getGrades();
        setGrades(response.data);
      } catch (error) {
        console.warn('Using demo data for grades');
        setGrades(demoGrades);
      } finally {
        setLoading(false);
      }
    };

    fetchGrades();
  }, []);

  const handleGradeSubmission = async (gradeData: { score: number; feedback: string }) => {
    if (!selectedGrade) return;

    try {
      // In a real app, this would call the API
      // await gradeAPI.updateGrade(selectedGrade.id, gradeData);

      // Update local state for demo
      setGrades(prev => prev.map(grade =>
        grade.id === selectedGrade.id
          ? {
              ...grade,
              score: gradeData.score,
              feedback: gradeData.feedback,
              status: 'graded' as const,
              gradedAt: new Date().toISOString(),
              gradedBy: user?.email || 'Current User'
            }
          : grade
      ));
    } catch (error) {
      console.error('Error updating grade:', error);
    }
  };

  const filteredGrades = grades.filter(grade => {
    const matchesFilter = filter === 'all' || grade.status === filter;
    const matchesSearch = searchTerm === '' ||
      grade.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      grade.studentId.includes(searchTerm) ||
      grade.scheduleTitle.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'graded': return '#27ae60';
      case 'reviewed': return '#3498db';
      default: return '#95a5a6';
    }
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return '#27ae60';
    if (percentage >= 80) return '#2ecc71';
    if (percentage >= 70) return '#f39c12';
    if (percentage >= 60) return '#e67e22';
    return '#e74c3c';
  };

  if (loading) {
    return (
      <div className="grades-loading">
        <div className="loading-spinner"></div>
        <p>Loading grades...</p>
      </div>
    );
  }

  return (
    <div className="grades">
      <div className="grades-header">
        <h1>Grading System</h1>
        <p>Review and grade student submissions</p>
      </div>

      <div className="grades-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search by student name, ID, or assignment..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-tabs">
          <button
            className={`filter-tab ${filter === 'pending' ? 'active' : ''}`}
            onClick={() => setFilter('pending')}
          >
            Pending ({grades.filter(g => g.status === 'pending').length})
          </button>
          <button
            className={`filter-tab ${filter === 'graded' ? 'active' : ''}`}
            onClick={() => setFilter('graded')}
          >
            Graded ({grades.filter(g => g.status === 'graded').length})
          </button>
          <button
            className={`filter-tab ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All ({grades.length})
          </button>
        </div>
      </div>

      <div className="grades-grid">
        {filteredGrades.map((grade) => (
          <div key={grade.id} className="grade-card">
            <div className="grade-header">
              <div className="student-info">
                <h3>{grade.studentName}</h3>
                <span className="student-id">{grade.studentId}</span>
              </div>
              <div
                className="grade-status"
                style={{ backgroundColor: getStatusColor(grade.status) }}
              >
                {grade.status.toUpperCase()}
              </div>
            </div>

            <div className="assignment-info">
              <h4>{grade.scheduleTitle}</h4>
              <div className="assignment-details">
                <span>{grade.labName} • {grade.className}</span>
                {grade.groupName && <span> • {grade.groupName}</span>}
              </div>
            </div>

            <div className="submission-summary">
              <div className="submission-type">
                Type: {grade.submissionType}
              </div>
              <div className="submission-date">
                Submitted: {new Date(grade.submittedAt).toLocaleDateString()}
              </div>
              {grade.files.length > 0 && (
                <div className="file-count">
                  Files: {grade.files.length}
                </div>
              )}
            </div>

            {grade.status === 'graded' && grade.score !== undefined && (
              <div className="grade-display">
                <div className="score-section">
                  <span
                    className="score"
                    style={{ color: getScoreColor(grade.score, grade.maxScore) }}
                  >
                    {grade.score}/{grade.maxScore}
                  </span>
                  <span className="percentage">
                    ({Math.round((grade.score / grade.maxScore) * 100)}%)
                  </span>
                </div>
                {grade.feedback && (
                  <div className="feedback-preview">
                    {grade.feedback.substring(0, 100)}...
                  </div>
                )}
                <div className="graded-info">
                  Graded by {grade.gradedBy} on {new Date(grade.gradedAt!).toLocaleDateString()}
                </div>
              </div>
            )}

            <div className="grade-actions">
              <button
                className="btn btn-outline"
                onClick={() => setSelectedGrade(grade)}
              >
                {grade.status === 'pending' ? 'Grade' : 'Review'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredGrades.length === 0 && (
        <div className="no-grades">
          <h3>No submissions found</h3>
          <p>There are no submissions matching your current filter and search criteria.</p>
        </div>
      )}

      {selectedGrade && (
        <GradingModal
          grade={selectedGrade}
          onClose={() => setSelectedGrade(null)}
          onSave={handleGradeSubmission}
        />
      )}
    </div>
  );
};

export default Grades;
