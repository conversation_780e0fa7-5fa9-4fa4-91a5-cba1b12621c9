.labs {
  padding: 2rem;
  width: 100%;
  margin: 0;
}

.labs-header {
  margin-bottom: 2rem;
  text-align: center;
}

.labs-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.labs-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.labs-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.labs-content {
  margin-top: 2rem;
}

.labs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.lab-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.lab-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.lab-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e8ed;
}

.lab-card-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.lab-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.lab-status.active {
  background-color: #d4edda;
  color: #155724;
}

.lab-status.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.lab-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat {
  text-align: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 500;
}

.lab-location {
  margin-bottom: 1rem;
  color: #5a6c7d;
  font-size: 0.95rem;
}

.lab-location strong {
  color: #2c3e50;
}

.lab-equipment {
  margin-bottom: 1.5rem;
}

.lab-equipment strong {
  color: #2c3e50;
  display: block;
  margin-bottom: 0.5rem;
}

.equipment-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.equipment-tag {
  background-color: #e3f2fd;
  color: #1565c0;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.lab-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .labs {
    padding: 1rem;
  }
  
  .labs-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .lab-card {
    padding: 1rem;
  }
  
  .lab-stats {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .lab-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .labs-header h1 {
    font-size: 2rem;
  }
  
  .labs-header p {
    font-size: 1rem;
  }
  
  .equipment-tags {
    justify-content: center;
  }
}
