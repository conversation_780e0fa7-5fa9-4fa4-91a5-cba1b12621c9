import React, { useState, useEffect } from 'react';
import { labsAPI } from '../../services/api';
import './Labs.css';

interface Lab {
  id: string;
  name: string;
  totalComputers: number;
  totalSeats: number;
  location: string;
  equipment: string[];
  isActive: boolean;
  availableComputers: number;
  availableSeats: number;
}

const Labs: React.FC = () => {
  const [labs, setLabs] = useState<Lab[]>([]);
  const [loading, setLoading] = useState(true);

  // Demo data for when backend is not available
  const demoLabs: Lab[] = [
    {
      id: '1',
      name: 'Lab 1',
      totalComputers: 15,
      totalSeats: 50,
      location: 'Computer Science Building - Ground Floor',
      equipment: ['Projector', 'Whiteboard', 'Air Conditioning', 'WiFi'],
      isActive: true,
      availableComputers: 12,
      availableSeats: 45
    },
    {
      id: '2',
      name: 'Lab 2',
      totalComputers: 19,
      totalSeats: 50,
      location: 'Computer Science Building - First Floor',
      equipment: ['Smart Board', 'Projector', 'Air Conditioning', 'WiFi'],
      isActive: true,
      availableComputers: 16,
      availableSeats: 42
    }
  ];

  useEffect(() => {
    const fetchLabs = async () => {
      try {
        setLoading(true);
        const response = await labsAPI.getLabs();
        // The API returns data in response.data.labs format
        const labsData = response.data.labs || response.data;

        // Transform the API data to match our interface
        const transformedLabs = labsData.map((lab: any) => ({
          id: lab.id,
          name: lab.name,
          totalComputers: lab.total_computers,
          totalSeats: lab.total_seats,
          location: lab.location,
          equipment: lab.equipment || [],
          isActive: lab.is_active,
          availableComputers: lab.functional_computers || lab.total_computers,
          availableSeats: lab.total_seats - Math.floor(Math.random() * 10) // Mock available seats
        }));

        setLabs(transformedLabs);
      } catch (error) {
        console.error('Error fetching labs:', error);
        console.warn('Using demo data for labs');
        setLabs(demoLabs);
      } finally {
        setLoading(false);
      }
    };

    fetchLabs();
  }, []);

  if (loading) {
    return (
      <div className="labs-loading">
        <div className="loading-spinner"></div>
        <p>Loading labs...</p>
      </div>
    );
  }

  // Safety check to ensure labs is an array
  const labsArray = Array.isArray(labs) ? labs : [];

  return (
    <div className="labs">
      <div className="labs-header">
        <h1>Laboratory Management</h1>
        <p>Monitor and manage computer labs, equipment, and resources</p>
      </div>

      <div className="labs-content">
        {labsArray.length === 0 ? (
          <div className="no-labs">
            <p>No labs available at the moment.</p>
          </div>
        ) : (
          <div className="labs-grid">
            {labsArray.map((lab) => (
            <div key={lab.id} className="lab-card">
              <div className="lab-card-header">
                <h3>{lab.name}</h3>
                <div className={`lab-status ${lab.isActive ? 'active' : 'inactive'}`}>
                  {lab.isActive ? 'Active' : 'Inactive'}
                </div>
              </div>

              <div className="lab-stats">
                <div className="stat">
                  <div className="stat-value">{lab.availableComputers}/{lab.totalComputers}</div>
                  <div className="stat-label">Computers Available</div>
                </div>
                <div className="stat">
                  <div className="stat-value">{lab.availableSeats}/{lab.totalSeats}</div>
                  <div className="stat-label">Seats Available</div>
                </div>
              </div>

              <div className="lab-location">
                <strong>Location:</strong> {lab.location}
              </div>

              <div className="lab-equipment">
                <strong>Equipment:</strong>
                <div className="equipment-tags">
                  {lab.equipment.map((item, index) => (
                    <span key={index} className="equipment-tag">{item}</span>
                  ))}
                </div>
              </div>

              <div className="lab-actions">
                <button className="btn btn-primary">View Details</button>
                <button className="btn btn-secondary">Schedule</button>
              </div>
            </div>
          ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Labs;
