.users {
  padding: 2rem;
  width: 100%;
  margin: 0;
}

.users-header {
  margin-bottom: 2rem;
}

.users-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.users-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.users-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 3rem;
  color: #e74c3c;
}

.error-message h3 {
  color: #c0392b;
  margin-bottom: 1rem;
}

.users-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem 1rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 2px solid #ecf0f1;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #7f8c8d;
}

.filter-tab:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-tab.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

.users-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
}

.users-table td {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: middle;
}

.users-table tr:hover {
  background: #f8f9fa;
}

.user-name .name-cell {
  display: flex;
  align-items: center;
}

.full-name {
  font-weight: 600;
  color: #2c3e50;
}

.user-email {
  color: #7f8c8d;
  font-family: monospace;
}

.role-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.role-admin {
  background: #e74c3c;
  color: white;
}

.role-instructor {
  background: #f39c12;
  color: white;
}

.role-student {
  background: #3498db;
  color: white;
}

.role-default {
  background: #95a5a6;
  color: white;
}

.student-id {
  font-family: monospace;
  font-weight: 600;
  color: #2c3e50;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background: #27ae60;
  color: white;
}

.status-badge.inactive {
  background: #e74c3c;
  color: white;
}

.created-date {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.user-actions {
  text-align: center;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  border: 2px solid #3498db;
  color: #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
  transform: translateY(-1px);
}

.no-users {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.no-users h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.users-summary {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .users {
    padding: 1rem;
  }
  
  .users-table-container {
    overflow-x: auto;
  }
  
  .users-table {
    min-width: 800px;
  }
  
  .filter-tabs {
    flex-direction: column;
  }
  
  .filter-tab {
    text-align: center;
  }
  
  .search-input {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .users-header h1 {
    font-size: 2rem;
  }
  
  .users-controls {
    padding: 1rem;
  }
  
  .users-table {
    min-width: 600px;
  }
}
