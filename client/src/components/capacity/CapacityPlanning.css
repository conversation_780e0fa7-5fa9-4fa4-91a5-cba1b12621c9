.capacity-planning {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.capacity-header {
  margin-bottom: 2rem;
}

.capacity-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.capacity-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.capacity-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 250px;
}

.control-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.control-group select {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.control-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.lab-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.lab-info h2 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.lab-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.capacity-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e1e8ed;
}

.tab-button {
  padding: 1rem 2rem;
  border: none;
  background: none;
  font-size: 1rem;
  font-weight: 600;
  color: #7f8c8d;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #3498db;
}

.tab-button.active {
  color: #3498db;
  border-bottom-color: #3498db;
}

.seats-section {
  margin-bottom: 2rem;
}

.seats-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.seats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.seat-card,
.computer-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.seat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.seat-card.available {
  border-color: #27ae60;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.seat-card.assigned {
  border-color: #3498db;
  background: linear-gradient(135deg, #f0f8ff 0%, #e1f0ff 100%);
}

.seat-name {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.seat-number {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 1rem;
}

.assignment-info {
  padding: 0.75rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
}

.student-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.student-id {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.available-indicator {
  padding: 0.75rem;
  background: rgba(39, 174, 96, 0.1);
  border-radius: 8px;
  color: #27ae60;
  font-weight: 600;
  font-size: 0.9rem;
}

.computer-status {
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.computer-status.functional {
  background-color: #d1fae5;
  color: #065f46;
}

.computer-status.non-functional {
  background-color: #fee2e2;
  color: #991b1b;
}

.computer-card.non-functional {
  opacity: 0.7;
  border-color: #fca5a5;
}

.computers-section {
  margin-bottom: 2rem;
}

.computers-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.computers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.computer-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.computer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.computer-card.functional {
  border-color: #27ae60;
}

.computer-card.non-functional {
  border-color: #e74c3c;
  opacity: 0.6;
}

.computer-card.assigned {
  border-color: #3498db;
  background: linear-gradient(135deg, #f0f8ff 0%, #e1f0ff 100%);
}

.computer-name {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.computer-status {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  display: inline-block;
}

.computer-status.functional {
  background: #d5f4e6;
  color: #27ae60;
}

.computer-status.non-functional {
  background: #ffeaea;
  color: #e74c3c;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .capacity-planning {
    padding: 1rem;
  }

  .capacity-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .control-group {
    min-width: auto;
  }

  .lab-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .seats-grid,
  .computers-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .seat-card,
  .computer-card {
    padding: 1rem;
  }

  .capacity-tabs {
    flex-direction: column;
    gap: 0;
  }

  .tab-button {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e1e8ed;
  }

  .tab-button.active {
    border-bottom-color: #3498db;
  }
}
