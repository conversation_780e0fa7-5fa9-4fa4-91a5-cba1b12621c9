.capacity-planning {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.capacity-header {
  margin-bottom: 2rem;
}

.capacity-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.capacity-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.capacity-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 250px;
}

.control-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.control-group select {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.control-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.lab-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.lab-info h2 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.lab-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.capacity-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e1e8ed;
}

.tab-button {
  padding: 1rem 2rem;
  border: none;
  background: none;
  font-size: 1rem;
  font-weight: 600;
  color: #7f8c8d;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #3498db;
}

.tab-button.active {
  color: #3498db;
  border-bottom-color: #3498db;
}

.seats-section {
  margin-bottom: 2rem;
}

.seats-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.seats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.seat-card,
.computer-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.seat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Color-coded seat statuses */
.seat-card.seat-available {
  border-color: #6c757d;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.seat-card.seat-available:hover {
  border-color: #007bff;
}

.seat-card.seat-reserved {
  border-color: #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.seat-card.seat-reserved:hover {
  border-color: #1e7e34;
}

.seat-card.seat-maintenance {
  border-color: #dc3545;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
  cursor: not-allowed;
}

.seat-card.seat-maintenance:hover {
  border-color: #c82333;
  transform: none;
}

.seat-icon {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

/* Status-based seat icon colors */
.seat-available .seat-icon {
  filter: grayscale(100%);
}

.seat-reserved .seat-icon {
  filter: hue-rotate(120deg) saturate(1.5);
}

.seat-maintenance .seat-icon {
  filter: hue-rotate(0deg) saturate(1.5) brightness(0.8);
}

.seat-name {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.seat-number {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 1rem;
}

.assignment-info {
  padding: 0.75rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
}

.student-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.student-id {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.available-indicator {
  padding: 0.75rem;
  background: rgba(39, 174, 96, 0.1);
  border-radius: 8px;
  color: #27ae60;
  font-weight: 600;
  font-size: 0.9rem;
}

.status-indicator {
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.75rem;
  border-radius: 8px;
  margin-top: 8px;
}

.status-indicator.status-available {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.1);
}

.status-indicator.status-reserved {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
}

.status-indicator.status-maintenance {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.computer-status {
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.computer-status.functional {
  background-color: #d1fae5;
  color: #065f46;
}

.computer-status.non-functional {
  background-color: #fee2e2;
  color: #991b1b;
}

.computer-card.non-functional {
  opacity: 0.7;
  border-color: #fca5a5;
}

.computers-section {
  margin-bottom: 2rem;
}

.computers-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.computers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.computer-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.computer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.computer-card.functional {
  border-color: #27ae60;
}

.computer-card.non-functional {
  border-color: #e74c3c;
  opacity: 0.6;
}

.computer-card.assigned {
  border-color: #3498db;
  background: linear-gradient(135deg, #f0f8ff 0%, #e1f0ff 100%);
}

.computer-name {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.computer-status {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  display: inline-block;
}

.computer-status.functional {
  background: #d5f4e6;
  color: #27ae60;
}

.computer-status.non-functional {
  background: #ffeaea;
  color: #e74c3c;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .capacity-planning {
    padding: 1rem;
  }

  .capacity-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .control-group {
    min-width: auto;
  }

  .lab-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .seats-grid,
  .computers-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .seat-card,
  .computer-card {
    padding: 1rem;
  }

  .capacity-tabs {
    flex-direction: column;
    gap: 0;
  }

  .tab-button {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e1e8ed;
  }

  .tab-button.active {
    border-bottom-color: #3498db;
  }
}

/* Enhanced assignment interface */
.assignment-interface {
  margin-top: 1rem;
}

.groups-assignments {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.groups-panel,
.computers-panel {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.groups-panel h4,
.computers-panel h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Groups styling */
.groups-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.group-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.group-header h5 {
  margin: 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.member-count {
  background: #e5e7eb;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.group-members {
  margin-bottom: 0.75rem;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
  font-size: 0.875rem;
}

.member-name {
  font-weight: 500;
  color: #374151;
}

.member-id {
  color: #6b7280;
}

.leader-badge {
  background: #fbbf24;
  color: #92400e;
  padding: 0.125rem 0.375rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.group-assignment {
  padding: 0.5rem;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
}

.assigned-computer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.computer-label {
  font-size: 0.875rem;
  color: #374151;
}

.computer-name {
  font-weight: 600;
  color: #1d4ed8;
}

.no-assignment {
  padding: 0.5rem;
  background: #fef3c7;
  border: 1px solid #fcd34d;
  border-radius: 4px;
  text-align: center;
  color: #92400e;
  font-size: 0.875rem;
}

.no-groups {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-class-selected {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 8px;
  margin-top: 1rem;
}

/* Enhanced computers grid */
.computers-panel .computers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  max-height: 500px;
  overflow-y: auto;
}

.computers-panel .computer-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
  min-height: 140px;
}

.computers-panel .computer-card.functional.available {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.computers-panel .computer-card.assigned {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.computer-number {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.assigned-to {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.assignment-type {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.schedule-info {
  font-size: 0.75rem;
  color: #6b7280;
}

.available-indicator {
  color: #10b981;
  font-weight: 500;
  text-align: center;
  padding: 0.5rem;
  background-color: #f0fdf4;
  border-radius: 4px;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

/* Students List Styles */
.students-list {
  margin-bottom: 2rem;
}

.students-list h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.students-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.student-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.student-card:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.student-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.student-id {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.student-role {
  color: #3498db;
  font-size: 0.8rem;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.assigned-seat {
  background-color: #d5f4e6;
  color: #27ae60;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

.assign-seat-btn, .assign-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  width: 100%;
}

.assign-seat-btn:hover, .assign-btn:hover {
  background-color: #2980b9;
}

.assign-seat-btn:disabled, .assign-btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.no-class-selected {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e1e8ed;
}

.no-class-selected p {
  font-size: 1.1rem;
  margin: 0;
}
