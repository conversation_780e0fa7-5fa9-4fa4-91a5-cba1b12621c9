import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import './CapacityPlanning.css';

interface Lab {
  id: string;
  name: string;
  total_computers: number;
  total_seats: number;
  location: string;
}

interface Computer {
  id: string;
  computer_name: string;
  computer_number: number;
  is_functional: boolean;
  specifications: any;
  assignment_id?: string;
  group_id?: string;
  user_id?: string;
  group_name?: string;
  first_name?: string;
  last_name?: string;
  student_id?: string;
}

interface Seat {
  id: string;
  seat_number: number;
  is_available: boolean;
}

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  student_id: string;
  email: string;
}

interface Class {
  id: string;
  name: string;
  grade: number;
  stream: string;
  capacity: number;
}

interface SeatAssignment {
  id: string;
  user_id: string;
  group_id?: string;
  seat_number: number;
  student_name: string;
  student_id: string;
  group_name?: string;
}

interface ComputerAssignment {
  id: string;
  group_id?: string;
  user_id?: string;
  computer_id: string;
  computer_name: string;
  assignment_type: 'group' | 'individual';
  group_name?: string;
  student_name?: string;
}

const CapacityPlanning: React.FC = () => {
  const { user } = useAuth();
  const [labs, setLabs] = useState<Lab[]>([]);
  const [selectedLab, setSelectedLab] = useState<string>('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [classes, setClasses] = useState<Class[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [computers, setComputers] = useState<Computer[]>([]);
  const [seats, setSeats] = useState<Seat[]>([]);
  const [seatAssignments, setSeatAssignments] = useState<SeatAssignment[]>([]);
  const [computerAssignments, setComputerAssignments] = useState<ComputerAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'seats' | 'computers'>('seats');

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      fetchStudents();
    }
  }, [selectedClass]);

  useEffect(() => {
    if (selectedLab) {
      fetchLabResources();
    }
  }, [selectedLab]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      
      // Fetch labs
      const labsResponse = await fetch('/api/labs', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const labsData = await labsResponse.json();
      setLabs(labsData);

      // Fetch classes
      const classesResponse = await fetch('/api/classes', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const classesData = await classesResponse.json();
      setClasses(classesData);

    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStudents = async () => {
    if (!selectedClass) return;
    
    try {
      const response = await fetch(`/api/users?role=student&classId=${selectedClass}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setStudents(data.users || []);
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  const fetchLabResources = async () => {
    if (!selectedLab) return;

    try {
      // Fetch lab details including computers and seats
      const response = await fetch(`/api/labs/${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();

      if (data.lab) {
        setComputers(data.lab.computers || []);
        setSeats(data.lab.seats || []);
      }

      // Fetch seat assignments
      fetchSeatAssignments();

    } catch (error) {
      console.error('Error fetching lab resources:', error);
    }
  };

  const generateSeatName = (labName: string, seatNumber: number): string => {
    const labCode = labName.includes('Computer Lab 1') ? 'CL1' : 
                   labName.includes('Computer Lab 2') ? 'CL2' : 
                   labName.includes('Programming Lab') ? 'PL' : 'RL';
    return `${labCode}-CR-${seatNumber.toString().padStart(3, '0')}`;
  };

  const assignSeat = async (studentId: string, seatId: string) => {
    try {
      const response = await fetch('/api/capacity/seat-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          user_id: studentId,
          seat_id: seatId
        })
      });

      if (response.ok) {
        // Refresh seat assignments
        fetchSeatAssignments();
      }
    } catch (error) {
      console.error('Error assigning seat:', error);
    }
  };

  const fetchSeatAssignments = async () => {
    if (!selectedLab) return;

    try {
      const response = await fetch(`/api/capacity/labs/${selectedLab}/seat-assignments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setSeatAssignments(data);
    } catch (error) {
      console.error('Error fetching seat assignments:', error);
    }
  };

  const selectedLabData = labs.find(lab => lab.id === selectedLab);

  if (loading) {
    return <div className="loading">Loading capacity planning...</div>;
  }

  return (
    <div className="capacity-planning">
      <div className="capacity-header">
        <h1>Capacity Planning</h1>
        <p>Assign students to seats and groups to computers</p>
      </div>

      <div className="capacity-controls">
        <div className="control-group">
          <label htmlFor="lab-select">Select Lab:</label>
          <select
            id="lab-select"
            value={selectedLab}
            onChange={(e) => setSelectedLab(e.target.value)}
          >
            <option value="">Choose a lab...</option>
            {labs.map(lab => (
              <option key={lab.id} value={lab.id}>
                {lab.name} - {lab.location}
              </option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="class-select">Select Class:</label>
          <select
            id="class-select"
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
          >
            <option value="">Choose a class...</option>
            {classes.map(cls => (
              <option key={cls.id} value={cls.id}>
                {cls.name} ({cls.stream})
              </option>
            ))}
          </select>
        </div>
      </div>

      {selectedLab && selectedLabData && (
        <div className="lab-info">
          <h2>{selectedLabData.name}</h2>
          <div className="lab-stats">
            <div className="stat">
              <span className="stat-label">Total Computers:</span>
              <span className="stat-value">{selectedLabData.total_computers}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Total Seats:</span>
              <span className="stat-value">{selectedLabData.total_seats}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Location:</span>
              <span className="stat-value">{selectedLabData.location}</span>
            </div>
          </div>
        </div>
      )}

      {selectedLab && (
        <div className="capacity-tabs">
          <button
            className={`tab-button ${activeTab === 'seats' ? 'active' : ''}`}
            onClick={() => setActiveTab('seats')}
          >
            Seat Assignments
          </button>
          <button
            className={`tab-button ${activeTab === 'computers' ? 'active' : ''}`}
            onClick={() => setActiveTab('computers')}
          >
            Computer Assignments
          </button>
        </div>
      )}

      {selectedLab && activeTab === 'seats' && (
        <div className="seats-section">
          <h3>Seat Assignments</h3>
          <div className="seats-grid">
            {seats.map(seat => {
              const assignment = seatAssignments.find(a => a.seat_number === seat.seat_number);
              const seatName = generateSeatName(selectedLabData?.name || '', seat.seat_number);

              return (
                <div
                  key={seat.id}
                  className={`seat-card ${assignment ? 'assigned' : 'available'}`}
                >
                  <div className="seat-name">{seatName}</div>
                  <div className="seat-number">Seat {seat.seat_number}</div>
                  {assignment ? (
                    <div className="assignment-info">
                      <div className="student-name">{assignment.student_name}</div>
                      <div className="student-id">{assignment.student_id}</div>
                    </div>
                  ) : (
                    <div className="available-indicator">Available</div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {selectedLab && activeTab === 'computers' && (
        <div className="computers-section">
          <h3>Computer Assignments</h3>
          <div className="computers-grid">
            {computers.map(computer => {
              const hasAssignment = computer.assignment_id;

              return (
                <div
                  key={computer.id}
                  className={`computer-card ${computer.is_functional ? 'functional' : 'non-functional'} ${hasAssignment ? 'assigned' : ''}`}
                >
                  <div className="computer-name">{computer.computer_name}</div>
                  <div className={`computer-status ${computer.is_functional ? 'functional' : 'non-functional'}`}>
                    {computer.is_functional ? 'Functional' : 'Non-Functional'}
                  </div>
                  {hasAssignment ? (
                    <div className="assignment-info">
                      {computer.group_id ? (
                        <>
                          <div className="group-name">Group: {computer.group_name}</div>
                          <div className="assignment-type">Group Assignment</div>
                        </>
                      ) : (
                        <>
                          <div className="student-name">{computer.first_name} {computer.last_name}</div>
                          <div className="assignment-type">Individual Assignment</div>
                        </>
                      )}
                    </div>
                  ) : (
                    <div className="available-indicator">Available</div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default CapacityPlanning;
