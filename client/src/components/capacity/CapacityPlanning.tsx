import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import './CapacityPlanning.css';

interface Lab {
  id: string;
  name: string;
  total_computers: number;
  total_seats: number;
  location: string;
}

interface Computer {
  id: string;
  computer_name: string;
  computer_number: number;
  is_functional: boolean;
  specifications: any;
  assignment_id?: string;
  group_id?: string;
  user_id?: string;
  group_name?: string;
  first_name?: string;
  last_name?: string;
  student_id?: string;
}

interface Seat {
  id: string;
  seat_number: number;
  is_available: boolean;
}

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  student_id: string;
  email: string;
}

interface Class {
  id: string;
  name: string;
  grade: number;
  stream: string;
  capacity: number;
  group_count: number;
  student_count: number;
  schedule_count: number;
}

interface Group {
  id: string;
  name: string;
  class_id: string;
  max_members: number;
  member_count: number;
  leader_name: string;
  description?: string;
  members: GroupMember[];
}

interface GroupMember {
  id: string;
  first_name: string;
  last_name: string;
  student_id: string;
  role: 'leader' | 'member';
}

interface SeatAssignment {
  id: string;
  user_id: string;
  group_id?: string;
  seat_number: number;
  student_name: string;
  student_id: string;
  group_name?: string;
}

interface ComputerAssignment {
  id: string;
  group_id?: string;
  user_id?: string;
  computer_id: string;
  computer_name: string;
  assignment_type: 'group' | 'individual';
  group_name?: string;
  student_name?: string;
}

const CapacityPlanning: React.FC = () => {
  const { user } = useAuth();
  const [labs, setLabs] = useState<Lab[]>([]);
  const [selectedLab, setSelectedLab] = useState<string>('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [classes, setClasses] = useState<Class[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [computers, setComputers] = useState<Computer[]>([]);
  const [seats, setSeats] = useState<Seat[]>([]);
  const [seatAssignments, setSeatAssignments] = useState<SeatAssignment[]>([]);
  const [computerAssignments, setComputerAssignments] = useState<ComputerAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'seats' | 'computers'>('seats');

  // Group Management State
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [showEditGroupModal, setShowEditGroupModal] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [showComputerAssignmentModal, setShowComputerAssignmentModal] = useState(false);
  const [assigningGroupId, setAssigningGroupId] = useState<string>('');
  const [availableStudents, setAvailableStudents] = useState<any[]>([]);

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedLab) {
      fetchLabClasses();
      fetchLabResources();
    }
  }, [selectedLab]);

  useEffect(() => {
    if (selectedClass && selectedLab) {
      fetchClassAssignments();
    }
  }, [selectedClass, selectedLab]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);

      // Fetch labs
      const labsResponse = await fetch('/api/labs', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const labsData = await labsResponse.json();
      setLabs(labsData.labs || []);

    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchLabClasses = async () => {
    if (!selectedLab) return;

    try {
      // Fetch classes that are assigned to the selected lab
      const classesResponse = await fetch(`/api/classes?labId=${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const classesData = await classesResponse.json();
      setClasses(classesData.classes || []);

      // Reset selected class when lab changes
      setSelectedClass('');
      setGroups([]);
      setComputerAssignments([]);
    } catch (error) {
      console.error('Error fetching lab classes:', error);
    }
  };

  const fetchClassAssignments = async () => {
    if (!selectedClass || !selectedLab) return;

    try {
      const response = await fetch(`/api/classes/${selectedClass}/assignments?labId=${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();

      if (data.groups) {
        setGroups(data.groups);
      }

      if (data.assignments) {
        setComputerAssignments(data.assignments);
      }
    } catch (error) {
      console.error('Error fetching class assignments:', error);
    }
  };

  const fetchLabResources = async () => {
    if (!selectedLab) return;

    try {
      // Fetch lab details including computers and seats
      const response = await fetch(`/api/labs/${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();

      if (data.lab) {
        setComputers(data.lab.computers || []);
        setSeats(data.lab.seats || []);
      }

      // Fetch seat assignments
      fetchSeatAssignments();

    } catch (error) {
      console.error('Error fetching lab resources:', error);
    }
  };

  const generateSeatName = (labName: string, seatNumber: number): string => {
    const labCode = labName.includes('Computer Lab 1') ? 'CL1' :
                   labName.includes('Computer Lab 2') ? 'CL2' :
                   labName.includes('Programming Lab') ? 'PL' : 'RL';
    return `${labCode}-CR-${seatNumber.toString().padStart(3, '0')}`;
  };

  const getSeatStatus = (seat: any, assignment: any) => {
    // Check if seat is under maintenance (you can add maintenance logic here)
    if (!seat.is_available) {
      return 'maintenance';
    }

    // Check if seat is assigned/reserved
    if (assignment) {
      return 'reserved';
    }

    // Default to available
    return 'available';
  };

  const assignSeat = async (studentId: string, seatId: string) => {
    try {
      const response = await fetch('/api/capacity/seat-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          user_id: studentId,
          seat_id: seatId
        })
      });

      if (response.ok) {
        // Refresh seat assignments
        fetchSeatAssignments();
      }
    } catch (error) {
      console.error('Error assigning seat:', error);
    }
  };

  const fetchSeatAssignments = async () => {
    if (!selectedLab) return;

    try {
      const response = await fetch(`/api/capacity/labs/${selectedLab}/seat-assignments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setSeatAssignments(data);
    } catch (error) {
      console.error('Error fetching seat assignments:', error);
    }
  };

  const assignComputerToGroup = async (groupId: string, computerId: string) => {
    try {
      // First, we need to find or create a schedule for this class/lab combination
      const scheduleResponse = await fetch(`/api/schedules?classId=${selectedClass}&labId=${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      let scheduleId = null;
      if (scheduleResponse.ok) {
        const schedules = await scheduleResponse.json();
        if (schedules.schedules && schedules.schedules.length > 0) {
          scheduleId = schedules.schedules[0].id;
        }
      }

      // If no schedule exists, create a default one
      if (!scheduleId) {
        const createScheduleResponse = await fetch('/api/schedules', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            title: `Capacity Planning - ${classes.find(c => c.id === selectedClass)?.name}`,
            description: 'Auto-generated schedule for capacity planning',
            labId: selectedLab,
            classId: selectedClass,
            scheduledDate: new Date().toISOString().split('T')[0],
            startTime: '09:00',
            endTime: '17:00'
          })
        });

        if (createScheduleResponse.ok) {
          const newSchedule = await createScheduleResponse.json();
          scheduleId = newSchedule.schedule.id;
        } else {
          alert('Failed to create schedule for assignment');
          return;
        }
      }

      // Now create the assignment
      const response = await fetch('/api/assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          schedule_id: scheduleId,
          group_id: groupId,
          assigned_computer: parseInt(computerId),
          assignment_type: 'group'
        })
      });

      if (response.ok) {
        // Refresh assignments
        fetchClassAssignments();
        alert('Computer assigned successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to assign computer'}`);
      }
    } catch (error) {
      console.error('Error assigning computer:', error);
      alert('Error assigning computer');
    }
  };

  const assignSeatToStudent = async (studentId: string, seatId: string) => {
    try {
      // First, we need to find or create a schedule for this class/lab combination
      const scheduleResponse = await fetch(`/api/schedules?classId=${selectedClass}&labId=${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      let scheduleId = null;
      if (scheduleResponse.ok) {
        const schedules = await scheduleResponse.json();
        if (schedules.schedules && schedules.schedules.length > 0) {
          scheduleId = schedules.schedules[0].id;
        }
      }

      // If no schedule exists, create a default one
      if (!scheduleId) {
        const createScheduleResponse = await fetch('/api/schedules', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            title: `Capacity Planning - ${classes.find(c => c.id === selectedClass)?.name}`,
            description: 'Auto-generated schedule for capacity planning',
            labId: selectedLab,
            classId: selectedClass,
            scheduledDate: new Date().toISOString().split('T')[0],
            startTime: '09:00',
            endTime: '17:00'
          })
        });

        if (createScheduleResponse.ok) {
          const newSchedule = await createScheduleResponse.json();
          scheduleId = newSchedule.schedule.id;
        } else {
          alert('Failed to create schedule for assignment');
          return;
        }
      }

      const response = await fetch('/api/capacity/seat-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          user_id: studentId,
          seat_id: seatId,
          schedule_id: scheduleId
        })
      });

      if (response.ok) {
        // Refresh seat assignments
        fetchSeatAssignments();
        alert('Seat assigned successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to assign seat'}`);
      }
    } catch (error) {
      console.error('Error assigning seat:', error);
      alert('Error assigning seat');
    }
  };

  // Group Management Functions
  const createGroup = async (groupData: { name: string; maxMembers: number; description?: string }) => {
    try {
      const response = await fetch('/api/groups', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          groupName: groupData.name,
          classId: selectedClass,
          maxMembers: groupData.maxMembers,
          description: groupData.description
        })
      });

      if (response.ok) {
        fetchClassAssignments();
        setShowCreateGroupModal(false);
        alert('Group created successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to create group'}`);
      }
    } catch (error) {
      console.error('Error creating group:', error);
      alert('Error creating group');
    }
  };

  const updateGroup = async (groupId: string, groupData: { name: string; maxMembers: number; description?: string }) => {
    try {
      const response = await fetch(`/api/groups/${groupId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          groupName: groupData.name,
          maxMembers: groupData.maxMembers,
          description: groupData.description
        })
      });

      if (response.ok) {
        fetchClassAssignments();
        setShowEditGroupModal(false);
        setEditingGroup(null);
        alert('Group updated successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to update group'}`);
      }
    } catch (error) {
      console.error('Error updating group:', error);
      alert('Error updating group');
    }
  };

  const deleteGroup = async (groupId: string) => {
    if (!confirm('Are you sure you want to delete this group? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/groups/${groupId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        fetchClassAssignments();
        alert('Group deleted successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to delete group'}`);
      }
    } catch (error) {
      console.error('Error deleting group:', error);
      alert('Error deleting group');
    }
  };

  const removeMemberFromGroup = async (groupId: string, userId: string) => {
    if (!confirm('Are you sure you want to remove this member from the group?')) {
      return;
    }

    try {
      const response = await fetch(`/api/groups/${groupId}/members/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        fetchClassAssignments();
        alert('Member removed successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to remove member'}`);
      }
    } catch (error) {
      console.error('Error removing member:', error);
      alert('Error removing member');
    }
  };

  const openEditGroupModal = (group: Group) => {
    setEditingGroup(group);
    setShowEditGroupModal(true);
  };

  const openComputerAssignmentModal = (groupId: string) => {
    setAssigningGroupId(groupId);
    setShowComputerAssignmentModal(true);
  };

  const unassignComputerFromGroup = async (groupId: string) => {
    if (!confirm('Are you sure you want to unassign the computer from this group?')) {
      return;
    }

    try {
      const assignment = computerAssignments.find(a => a.group_id === groupId);
      if (!assignment) return;

      const response = await fetch(`/api/capacity/computer-assignments/${assignment.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        fetchClassAssignments();
        alert('Computer unassigned successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to unassign computer'}`);
      }
    } catch (error) {
      console.error('Error unassigning computer:', error);
      alert('Error unassigning computer');
    }
  };

  // Seat Assignment Functions
  const assignSeatToStudent = async (studentId: string, seatId: string) => {
    try {
      // First, we need to create or find a schedule for this class/lab combination
      const scheduleResponse = await fetch('/api/schedules', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!scheduleResponse.ok) {
        throw new Error('Failed to fetch schedules');
      }

      const schedules = await scheduleResponse.json();
      let schedule = schedules.find((s: any) =>
        s.class_id === selectedClass && s.lab_id === selectedLab
      );

      // If no schedule exists, create one
      if (!schedule) {
        const createScheduleResponse = await fetch('/api/schedules', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            title: `Capacity Planning - ${classes.find(c => c.id === selectedClass)?.name}`,
            description: 'Auto-generated schedule for capacity planning',
            class_id: selectedClass,
            lab_id: selectedLab,
            scheduled_date: new Date().toISOString().split('T')[0],
            start_time: '09:00',
            end_time: '17:00'
          })
        });

        if (!createScheduleResponse.ok) {
          throw new Error('Failed to create schedule');
        }

        schedule = await createScheduleResponse.json();
      }

      // Now create the seat assignment
      const response = await fetch('/api/capacity/seat-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          user_id: studentId,
          seat_id: seatId,
          schedule_id: schedule.id
        })
      });

      if (response.ok) {
        fetchLabResources();
        alert('Seat assigned successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to assign seat'}`);
      }
    } catch (error) {
      console.error('Error assigning seat:', error);
      alert('Error assigning seat');
    }
  };

  const unassignSeat = async (assignmentId: string) => {
    if (!confirm('Are you sure you want to unassign this seat?')) {
      return;
    }

    try {
      const response = await fetch(`/api/capacity/seat-assignments/${assignmentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        fetchLabResources();
        alert('Seat unassigned successfully!');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error || 'Failed to unassign seat'}`);
      }
    } catch (error) {
      console.error('Error unassigning seat:', error);
      alert('Error unassigning seat');
    }
  };

  if (loading) {
    return <div className="loading">Loading capacity planning...</div>;
  }

  const selectedLabData = Array.isArray(labs) ? labs.find(lab => lab.id === selectedLab) : undefined;

  return (
    <div className="capacity-planning">
      <div className="capacity-header">
        <h1>Capacity Planning</h1>
        <p>Assign students to seats and groups to computers</p>
      </div>

      <div className="capacity-controls">
        <div className="control-group">
          <label htmlFor="lab-select">Select Lab:</label>
          <select
            id="lab-select"
            value={selectedLab}
            onChange={(e) => setSelectedLab(e.target.value)}
          >
            <option value="">Choose a lab...</option>
            {Array.isArray(labs) && labs.map(lab => (
              <option key={lab.id} value={lab.id}>
                {lab.name} - {lab.location}
              </option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="class-select">Select Class:</label>
          <select
            id="class-select"
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
          >
            <option value="">Choose a class...</option>
            {Array.isArray(classes) && classes.map(cls => (
              <option key={cls.id} value={cls.id}>
                {cls.name} ({cls.stream})
              </option>
            ))}
          </select>
        </div>
      </div>

      {selectedLab && selectedLabData && (
        <div className="lab-info">
          <h2>{selectedLabData.name}</h2>
          <div className="lab-stats">
            <div className="stat">
              <span className="stat-label">Total Computers:</span>
              <span className="stat-value">{selectedLabData.total_computers}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Total Seats:</span>
              <span className="stat-value">{selectedLabData.total_seats}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Location:</span>
              <span className="stat-value">{selectedLabData.location}</span>
            </div>
          </div>
        </div>
      )}

      {selectedLab && (
        <div className="capacity-tabs">
          <button
            className={`tab-button ${activeTab === 'seats' ? 'active' : ''}`}
            onClick={() => setActiveTab('seats')}
          >
            Seat Assignments
          </button>
          <button
            className={`tab-button ${activeTab === 'computers' ? 'active' : ''}`}
            onClick={() => setActiveTab('computers')}
          >
            Computer Assignments
          </button>
        </div>
      )}

      {selectedLab && activeTab === 'seats' && (
        <div className="seats-section">
          <h3>Seat Assignments</h3>
          {selectedClass ? (
            <>
              <div className="students-list">
                <h4>Students in {classes.find(c => c.id === selectedClass)?.name}</h4>
                <div className="students-grid">
                  {groups.flatMap(group => group.members).map(student => (
                    <div key={student.id} className="student-card">
                      <div className="student-name">{student.first_name} {student.last_name}</div>
                      <div className="student-id">{student.student_id}</div>
                      <div className="student-role">{student.role}</div>
                      {seatAssignments.find(a => a.user_id === student.id) ? (
                        <div className="assigned-seat">
                          <span>Assigned to Seat {seatAssignments.find(a => a.user_id === student.id)?.seat_number}</span>
                          <button
                            className="unassign-btn"
                            onClick={() => {
                              const assignment = seatAssignments.find(a => a.user_id === student.id);
                              if (assignment) {
                                unassignSeat(assignment.assignment_id);
                              }
                            }}
                          >
                            Unassign
                          </button>
                        </div>
                      ) : (
                        <button
                          className="assign-seat-btn"
                          onClick={() => {
                            const availableSeats = seats.filter(s =>
                              s.is_available &&
                              !seatAssignments.find(a => a.seat_number === s.seat_number)
                            );
                            if (availableSeats.length > 0) {
                              const seatId = prompt(`Available seats:\n${availableSeats.map(s => `${s.id}: Seat ${s.seat_number}`).join('\n')}\n\nEnter seat ID:`);
                              if (seatId) {
                                assignSeatToStudent(student.id, seatId);
                              }
                            } else {
                              alert('No available seats');
                            }
                          }}
                        >
                          Assign Seat
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              <div className="seats-grid">
            {Array.isArray(seats) && seats.map(seat => {
              const assignment = Array.isArray(seatAssignments) ? seatAssignments.find(a => a.seat_number === seat.seat_number) : undefined;
              const seatName = generateSeatName(selectedLabData?.name || '', seat.seat_number);
              const seatStatus = getSeatStatus(seat, assignment);

              return (
                <div
                  key={seat.id}
                  className={`seat-card seat-${seatStatus}`}
                >
                  <div className="seat-icon">💺</div>
                  <div className="seat-name">{seatName}</div>
                  <div className="seat-number">Seat {seat.seat_number}</div>
                  {assignment ? (
                    <div className="assignment-info">
                      <div className="student-name">{assignment.student_name}</div>
                      <div className="student-id">{assignment.student_id}</div>
                      <button
                        className="unassign-btn"
                        onClick={() => unassignSeat(assignment.assignment_id)}
                      >
                        Unassign
                      </button>
                    </div>
                  ) : (
                    <div className={`status-indicator status-${seatStatus}`}>
                      {seatStatus === 'maintenance' ? 'Under Maintenance' :
                       seatStatus === 'reserved' ? 'Reserved' : 'Available'}
                      {seatStatus === 'available' && (
                        <button
                          className="assign-seat-btn"
                          onClick={() => {
                            const availableStudents = groups.flatMap(group => group.members).filter(student =>
                              !seatAssignments.find(a => a.user_id === student.id)
                            );
                            if (availableStudents.length > 0) {
                              const studentId = prompt(`Available students:\n${availableStudents.map(s => `${s.id}: ${s.first_name} ${s.last_name} (${s.student_id})`).join('\n')}\n\nEnter student ID:`);
                              if (studentId) {
                                assignSeatToStudent(studentId, seat.id);
                              }
                            } else {
                              alert('No unassigned students available');
                            }
                          }}
                        >
                          Assign Student
                        </button>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
              </div>
            </>
          ) : (
            <div className="no-class-selected">
              <p>Please select a class to view seat assignments.</p>
            </div>
          )}
        </div>
      )}

      {selectedLab && activeTab === 'computers' && (
        <div className="computers-section">
          <h3>Computer Assignments</h3>

          {selectedClass ? (
            <div className="assignment-interface">
              {/* Groups and Assignments Display */}
              <div className="groups-assignments">
                <div className="groups-panel">
                  <div className="groups-header">
                    <h4>Groups in {classes.find(c => c.id === selectedClass)?.name}</h4>
                    <button
                      className="create-group-btn"
                      onClick={() => setShowCreateGroupModal(true)}
                    >
                      Create New Group
                    </button>
                  </div>

                  {groups.length === 0 ? (
                    <div className="no-groups">
                      <p>No groups found for this class. Create a group to get started.</p>
                    </div>
                  ) : (
                    <div className="groups-list">
                      {groups.map(group => (
                        <div key={group.id} className="group-card">
                          <div className="group-header">
                            <h5>{group.name}</h5>
                            <div className="group-actions">
                              <span className="member-count">{group.member_count} members</span>
                              <button
                                className="edit-group-btn"
                                onClick={() => openEditGroupModal(group)}
                              >
                                Edit
                              </button>
                              <button
                                className="delete-group-btn"
                                onClick={() => deleteGroup(group.id)}
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                          <div className="group-members">
                            {group.members?.map(member => (
                              <div key={member.id} className="member-item">
                                <span className="member-name">
                                  {member.first_name} {member.last_name}
                                </span>
                                <span className="member-id">({member.student_id})</span>
                                {member.role === 'leader' && (
                                  <span className="leader-badge">Leader</span>
                                )}
                                {member.role !== 'leader' && (
                                  <button
                                    className="remove-member-btn"
                                    onClick={() => removeMemberFromGroup(group.id, member.id)}
                                  >
                                    Remove
                                  </button>
                                )}
                              </div>
                            ))}
                          </div>

                          {/* Computer Assignment for Group */}
                          <div className="group-assignment">
                            {computerAssignments.find(a => a.group_id === group.id) ? (
                              <div className="assigned-computer">
                                <span className="computer-label">Assigned Computer:</span>
                                <span className="computer-name">
                                  {computerAssignments.find(a => a.group_id === group.id)?.computer_name}
                                </span>
                                <button
                                  className="unassign-btn"
                                  onClick={() => unassignComputerFromGroup(group.id)}
                                >
                                  Unassign
                                </button>
                              </div>
                            ) : (
                              <div className="no-assignment">
                                <span>No computer assigned</span>
                                <button
                                  className="assign-btn"
                                  onClick={() => openComputerAssignmentModal(group.id)}
                                >
                                  Assign Computer
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="computers-panel">
                  <h4>Available Computers</h4>
                  <div className="computers-grid">
                    {Array.isArray(computers) && computers.map(computer => {
                      const assignment = computerAssignments.find(a =>
                        a.computer_name === computer.computer_name
                      );

                      return (
                        <div
                          key={computer.id}
                          className={`computer-card ${computer.is_functional ? 'functional' : 'non-functional'} ${assignment ? 'assigned' : 'available'}`}
                        >
                          <div className="computer-name">{computer.computer_name}</div>
                          <div className="computer-number">#{computer.computer_number}</div>
                          <div className={`computer-status ${computer.is_functional ? 'functional' : 'non-functional'}`}>
                            {computer.is_functional ? 'Functional' : 'Non-Functional'}
                          </div>

                          {assignment ? (
                            <div className="assignment-info">
                              {assignment.group_name ? (
                                <>
                                  <div className="assigned-to">Group: {assignment.group_name}</div>
                                  <div className="assignment-type">Group Assignment</div>
                                </>
                              ) : (
                                <>
                                  <div className="assigned-to">{assignment.student_name}</div>
                                  <div className="assignment-type">Individual Assignment</div>
                                </>
                              )}
                              <div className="schedule-info">
                                <small>{assignment.schedule_title}</small>
                              </div>
                            </div>
                          ) : (
                            <div className="available-indicator">
                              {computer.is_functional ? 'Available' : 'Under Maintenance'}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="no-class-selected">
              <p>Please select a class to view computer assignments and groups.</p>
            </div>
          )}
        </div>
      )}

      {/* Create Group Modal */}
      {showCreateGroupModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Create New Group</h3>
              <button
                className="close-btn"
                onClick={() => setShowCreateGroupModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                createGroup({
                  name: formData.get('groupName') as string,
                  maxMembers: parseInt(formData.get('maxMembers') as string),
                  description: formData.get('description') as string
                });
              }}>
                <div className="form-group">
                  <label htmlFor="groupName">Group Name:</label>
                  <input
                    type="text"
                    id="groupName"
                    name="groupName"
                    required
                    placeholder="Enter group name"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="maxMembers">Max Members:</label>
                  <input
                    type="number"
                    id="maxMembers"
                    name="maxMembers"
                    min="1"
                    max="10"
                    defaultValue="4"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="description">Description (Optional):</label>
                  <textarea
                    id="description"
                    name="description"
                    placeholder="Enter group description"
                    rows={3}
                  />
                </div>
                <div className="modal-actions">
                  <button type="button" onClick={() => setShowCreateGroupModal(false)}>
                    Cancel
                  </button>
                  <button type="submit" className="primary">
                    Create Group
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Group Modal */}
      {showEditGroupModal && editingGroup && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Edit Group</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setShowEditGroupModal(false);
                  setEditingGroup(null);
                }}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                updateGroup(editingGroup.id, {
                  name: formData.get('groupName') as string,
                  maxMembers: parseInt(formData.get('maxMembers') as string),
                  description: formData.get('description') as string
                });
              }}>
                <div className="form-group">
                  <label htmlFor="editGroupName">Group Name:</label>
                  <input
                    type="text"
                    id="editGroupName"
                    name="groupName"
                    defaultValue={editingGroup.name}
                    required
                    placeholder="Enter group name"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="editMaxMembers">Max Members:</label>
                  <input
                    type="number"
                    id="editMaxMembers"
                    name="maxMembers"
                    min="1"
                    max="10"
                    defaultValue={editingGroup.max_members}
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="editDescription">Description (Optional):</label>
                  <textarea
                    id="editDescription"
                    name="description"
                    defaultValue={editingGroup.description || ''}
                    placeholder="Enter group description"
                    rows={3}
                  />
                </div>
                <div className="modal-actions">
                  <button type="button" onClick={() => {
                    setShowEditGroupModal(false);
                    setEditingGroup(null);
                  }}>
                    Cancel
                  </button>
                  <button type="submit" className="primary">
                    Update Group
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Computer Assignment Modal */}
      {showComputerAssignmentModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Assign Computer to Group</h3>
              <button
                className="close-btn"
                onClick={() => {
                  setShowComputerAssignmentModal(false);
                  setAssigningGroupId('');
                }}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="available-computers">
                <h4>Available Computers:</h4>
                <div className="computers-grid">
                  {computers.filter(c =>
                    c.is_functional &&
                    !computerAssignments.find(a => a.computer_name === c.computer_name)
                  ).map(computer => (
                    <div
                      key={computer.id}
                      className="computer-option"
                      onClick={() => {
                        assignComputerToGroup(assigningGroupId, computer.id.toString());
                        setShowComputerAssignmentModal(false);
                        setAssigningGroupId('');
                      }}
                    >
                      <div className="computer-name">{computer.computer_name}</div>
                      <div className="computer-number">#{computer.computer_number}</div>
                      <div className="computer-status functional">Available</div>
                    </div>
                  ))}
                </div>
                {computers.filter(c =>
                  c.is_functional &&
                  !computerAssignments.find(a => a.computer_name === c.computer_name)
                ).length === 0 && (
                  <p>No available computers for assignment.</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CapacityPlanning;
