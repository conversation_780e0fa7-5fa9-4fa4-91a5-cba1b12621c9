import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import './CapacityPlanning.css';

interface Lab {
  id: string;
  name: string;
  total_computers: number;
  total_seats: number;
  location: string;
}

interface Computer {
  id: string;
  computer_name: string;
  computer_number: number;
  is_functional: boolean;
  specifications: any;
  assignment_id?: string;
  group_id?: string;
  user_id?: string;
  group_name?: string;
  first_name?: string;
  last_name?: string;
  student_id?: string;
}

interface Seat {
  id: string;
  seat_number: number;
  is_available: boolean;
}

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  student_id: string;
  email: string;
}

interface Class {
  id: string;
  name: string;
  grade: number;
  stream: string;
  capacity: number;
  group_count: number;
  student_count: number;
  schedule_count: number;
}

interface Group {
  id: string;
  group_name: string;
  class_id: string;
  max_members: number;
  member_count: number;
  creator_name: string;
  members: GroupMember[];
}

interface GroupMember {
  id: string;
  first_name: string;
  last_name: string;
  student_id: string;
  role: 'leader' | 'member';
}

interface SeatAssignment {
  id: string;
  user_id: string;
  group_id?: string;
  seat_number: number;
  student_name: string;
  student_id: string;
  group_name?: string;
}

interface ComputerAssignment {
  id: string;
  group_id?: string;
  user_id?: string;
  computer_id: string;
  computer_name: string;
  assignment_type: 'group' | 'individual';
  group_name?: string;
  student_name?: string;
}

const CapacityPlanning: React.FC = () => {
  const { user } = useAuth();
  const [labs, setLabs] = useState<Lab[]>([]);
  const [selectedLab, setSelectedLab] = useState<string>('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [classes, setClasses] = useState<Class[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [computers, setComputers] = useState<Computer[]>([]);
  const [seats, setSeats] = useState<Seat[]>([]);
  const [seatAssignments, setSeatAssignments] = useState<SeatAssignment[]>([]);
  const [computerAssignments, setComputerAssignments] = useState<ComputerAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'seats' | 'computers'>('seats');

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedLab) {
      fetchLabClasses();
      fetchLabResources();
    }
  }, [selectedLab]);

  useEffect(() => {
    if (selectedClass && selectedLab) {
      fetchClassAssignments();
    }
  }, [selectedClass, selectedLab]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);

      // Fetch labs
      const labsResponse = await fetch('/api/labs', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const labsData = await labsResponse.json();
      setLabs(labsData.labs || []);

    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchLabClasses = async () => {
    if (!selectedLab) return;

    try {
      // Fetch classes that are assigned to the selected lab
      const classesResponse = await fetch(`/api/classes?labId=${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const classesData = await classesResponse.json();
      setClasses(classesData.classes || []);

      // Reset selected class when lab changes
      setSelectedClass('');
      setGroups([]);
      setComputerAssignments([]);
    } catch (error) {
      console.error('Error fetching lab classes:', error);
    }
  };

  const fetchClassAssignments = async () => {
    if (!selectedClass || !selectedLab) return;

    try {
      const response = await fetch(`/api/classes/${selectedClass}/assignments?labId=${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();

      if (data.groups) {
        setGroups(data.groups);
      }

      if (data.assignments) {
        setComputerAssignments(data.assignments);
      }
    } catch (error) {
      console.error('Error fetching class assignments:', error);
    }
  };

  const fetchLabResources = async () => {
    if (!selectedLab) return;

    try {
      // Fetch lab details including computers and seats
      const response = await fetch(`/api/labs/${selectedLab}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();

      if (data.lab) {
        setComputers(data.lab.computers || []);
        setSeats(data.lab.seats || []);
      }

      // Fetch seat assignments
      fetchSeatAssignments();

    } catch (error) {
      console.error('Error fetching lab resources:', error);
    }
  };

  const generateSeatName = (labName: string, seatNumber: number): string => {
    const labCode = labName.includes('Computer Lab 1') ? 'CL1' :
                   labName.includes('Computer Lab 2') ? 'CL2' :
                   labName.includes('Programming Lab') ? 'PL' : 'RL';
    return `${labCode}-CR-${seatNumber.toString().padStart(3, '0')}`;
  };

  const getSeatStatus = (seat: any, assignment: any) => {
    // Check if seat is under maintenance (you can add maintenance logic here)
    if (!seat.is_available) {
      return 'maintenance';
    }

    // Check if seat is assigned/reserved
    if (assignment) {
      return 'reserved';
    }

    // Default to available
    return 'available';
  };

  const assignSeat = async (studentId: string, seatId: string) => {
    try {
      const response = await fetch('/api/capacity/seat-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          user_id: studentId,
          seat_id: seatId
        })
      });

      if (response.ok) {
        // Refresh seat assignments
        fetchSeatAssignments();
      }
    } catch (error) {
      console.error('Error assigning seat:', error);
    }
  };

  const fetchSeatAssignments = async () => {
    if (!selectedLab) return;

    try {
      const response = await fetch(`/api/capacity/labs/${selectedLab}/seat-assignments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setSeatAssignments(data);
    } catch (error) {
      console.error('Error fetching seat assignments:', error);
    }
  };

  if (loading) {
    return <div className="loading">Loading capacity planning...</div>;
  }

  const selectedLabData = Array.isArray(labs) ? labs.find(lab => lab.id === selectedLab) : undefined;

  return (
    <div className="capacity-planning">
      <div className="capacity-header">
        <h1>Capacity Planning</h1>
        <p>Assign students to seats and groups to computers</p>
      </div>

      <div className="capacity-controls">
        <div className="control-group">
          <label htmlFor="lab-select">Select Lab:</label>
          <select
            id="lab-select"
            value={selectedLab}
            onChange={(e) => setSelectedLab(e.target.value)}
          >
            <option value="">Choose a lab...</option>
            {Array.isArray(labs) && labs.map(lab => (
              <option key={lab.id} value={lab.id}>
                {lab.name} - {lab.location}
              </option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="class-select">Select Class:</label>
          <select
            id="class-select"
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
          >
            <option value="">Choose a class...</option>
            {Array.isArray(classes) && classes.map(cls => (
              <option key={cls.id} value={cls.id}>
                {cls.name} ({cls.stream})
              </option>
            ))}
          </select>
        </div>
      </div>

      {selectedLab && selectedLabData && (
        <div className="lab-info">
          <h2>{selectedLabData.name}</h2>
          <div className="lab-stats">
            <div className="stat">
              <span className="stat-label">Total Computers:</span>
              <span className="stat-value">{selectedLabData.total_computers}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Total Seats:</span>
              <span className="stat-value">{selectedLabData.total_seats}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Location:</span>
              <span className="stat-value">{selectedLabData.location}</span>
            </div>
          </div>
        </div>
      )}

      {selectedLab && (
        <div className="capacity-tabs">
          <button
            className={`tab-button ${activeTab === 'seats' ? 'active' : ''}`}
            onClick={() => setActiveTab('seats')}
          >
            Seat Assignments
          </button>
          <button
            className={`tab-button ${activeTab === 'computers' ? 'active' : ''}`}
            onClick={() => setActiveTab('computers')}
          >
            Computer Assignments
          </button>
        </div>
      )}

      {selectedLab && activeTab === 'seats' && (
        <div className="seats-section">
          <h3>Seat Assignments</h3>
          <div className="seats-grid">
            {Array.isArray(seats) && seats.map(seat => {
              const assignment = Array.isArray(seatAssignments) ? seatAssignments.find(a => a.seat_number === seat.seat_number) : undefined;
              const seatName = generateSeatName(selectedLabData?.name || '', seat.seat_number);
              const seatStatus = getSeatStatus(seat, assignment);

              return (
                <div
                  key={seat.id}
                  className={`seat-card seat-${seatStatus}`}
                >
                  <div className="seat-icon">💺</div>
                  <div className="seat-name">{seatName}</div>
                  <div className="seat-number">Seat {seat.seat_number}</div>
                  {assignment ? (
                    <div className="assignment-info">
                      <div className="student-name">{assignment.student_name}</div>
                      <div className="student-id">{assignment.student_id}</div>
                    </div>
                  ) : (
                    <div className={`status-indicator status-${seatStatus}`}>
                      {seatStatus === 'maintenance' ? 'Under Maintenance' :
                       seatStatus === 'reserved' ? 'Reserved' : 'Available'}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {selectedLab && activeTab === 'computers' && (
        <div className="computers-section">
          <h3>Computer Assignments</h3>

          {selectedClass ? (
            <div className="assignment-interface">
              {/* Groups and Assignments Display */}
              <div className="groups-assignments">
                <div className="groups-panel">
                  <h4>Groups in {classes.find(c => c.id === selectedClass)?.name}</h4>
                  {groups.length === 0 ? (
                    <div className="no-groups">
                      <p>No groups found for this class.</p>
                    </div>
                  ) : (
                    <div className="groups-list">
                      {groups.map(group => (
                        <div key={group.id} className="group-card">
                          <div className="group-header">
                            <h5>{group.group_name}</h5>
                            <span className="member-count">{group.member_count} members</span>
                          </div>
                          <div className="group-members">
                            {group.members?.map(member => (
                              <div key={member.id} className="member-item">
                                <span className="member-name">
                                  {member.first_name} {member.last_name}
                                </span>
                                <span className="member-id">({member.student_id})</span>
                                {member.role === 'leader' && (
                                  <span className="leader-badge">Leader</span>
                                )}
                              </div>
                            ))}
                          </div>

                          {/* Show assigned computer for this group */}
                          {computerAssignments.find(a => a.group_id === group.id) ? (
                            <div className="group-assignment">
                              <div className="assigned-computer">
                                <span className="computer-label">Assigned Computer:</span>
                                <span className="computer-name">
                                  {computerAssignments.find(a => a.group_id === group.id)?.computer_name}
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div className="no-assignment">
                              <span>No computer assigned</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="computers-panel">
                  <h4>Available Computers</h4>
                  <div className="computers-grid">
                    {Array.isArray(computers) && computers.map(computer => {
                      const assignment = computerAssignments.find(a =>
                        a.computer_name === computer.computer_name
                      );

                      return (
                        <div
                          key={computer.id}
                          className={`computer-card ${computer.is_functional ? 'functional' : 'non-functional'} ${assignment ? 'assigned' : 'available'}`}
                        >
                          <div className="computer-name">{computer.computer_name}</div>
                          <div className="computer-number">#{computer.computer_number}</div>
                          <div className={`computer-status ${computer.is_functional ? 'functional' : 'non-functional'}`}>
                            {computer.is_functional ? 'Functional' : 'Non-Functional'}
                          </div>

                          {assignment ? (
                            <div className="assignment-info">
                              {assignment.group_name ? (
                                <>
                                  <div className="assigned-to">Group: {assignment.group_name}</div>
                                  <div className="assignment-type">Group Assignment</div>
                                </>
                              ) : (
                                <>
                                  <div className="assigned-to">{assignment.student_name}</div>
                                  <div className="assignment-type">Individual Assignment</div>
                                </>
                              )}
                              <div className="schedule-info">
                                <small>{assignment.schedule_title}</small>
                              </div>
                            </div>
                          ) : (
                            <div className="available-indicator">
                              {computer.is_functional ? 'Available' : 'Under Maintenance'}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="no-class-selected">
              <p>Please select a class to view computer assignments and groups.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CapacityPlanning;
