import React, { useState, useEffect } from 'react';
import './Groups.css';

interface GroupMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  studentId: string;
}

interface Group {
  id: string;
  name: string;
  description: string;
  leaderId: string;
  leaderName: string;
  classId: string;
  className: string;
  memberCount: number;
  members: GroupMember[];
  createdAt: string;
}

const Groups: React.FC = () => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);

  useEffect(() => {
    fetchGroups();
  }, []);

  const fetchGroups = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/groups', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch groups');
      }

      const data = await response.json();
      setGroups(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.className.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.leaderName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroup(expandedGroup === groupId ? null : groupId);
  };

  if (loading) {
    return (
      <div className="groups">
        <div className="groups-loading">
          <div className="loading-spinner"></div>
          <p>Loading groups...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="groups">
        <div className="error-message">
          <h3>Error Loading Groups</h3>
          <p>{error}</p>
          <button onClick={fetchGroups} className="btn btn-primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="groups">
      <div className="groups-header">
        <h1>Student Groups</h1>
        <p>Manage student groups and their members</p>
      </div>

      <div className="groups-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search groups by name, class, or leader..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="groups-stats">
          <div className="stat-item">
            <span className="stat-number">{groups.length}</span>
            <span className="stat-label">Total Groups</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {groups.reduce((sum, group) => sum + group.memberCount, 0)}
            </span>
            <span className="stat-label">Total Members</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {Math.round(groups.reduce((sum, group) => sum + group.memberCount, 0) / groups.length * 10) / 10 || 0}
            </span>
            <span className="stat-label">Avg Members/Group</span>
          </div>
        </div>
      </div>

      <div className="groups-table-container">
        <table className="groups-table">
          <thead>
            <tr>
              <th>Group Name</th>
              <th>Class</th>
              <th>Leader</th>
              <th>Members</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredGroups.map((group) => (
              <React.Fragment key={group.id}>
                <tr className="group-row">
                  <td className="group-name">
                    <div className="name-cell">
                      <span className="group-title">{group.name}</span>
                      {group.description && (
                        <span className="group-description">{group.description}</span>
                      )}
                    </div>
                  </td>
                  <td className="class-name">{group.className}</td>
                  <td className="leader-name">{group.leaderName}</td>
                  <td className="member-count">
                    <span className="count-badge">{group.memberCount}</span>
                  </td>
                  <td className="created-date">
                    {new Date(group.createdAt).toLocaleDateString()}
                  </td>
                  <td className="group-actions">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => toggleGroupExpansion(group.id)}
                    >
                      {expandedGroup === group.id ? 'Hide' : 'View'} Members
                    </button>
                  </td>
                </tr>

                {expandedGroup === group.id && (
                  <tr className="members-row">
                    <td colSpan={6}>
                      <div className="members-container">
                        <h4>Group Members</h4>
                        <div className="members-grid">
                          {group.members.map((member) => (
                            <div key={member.id} className="member-card">
                              <div className="member-info">
                                <span className="member-name">
                                  {member.firstName} {member.lastName}
                                </span>
                                <span className="member-id">ID: {member.studentId}</span>
                                <span className="member-email">{member.email}</span>
                              </div>
                              {member.id === group.leaderId && (
                                <span className="leader-badge">Leader</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>

        {filteredGroups.length === 0 && (
          <div className="no-groups">
            <h3>No groups found</h3>
            <p>No groups match your current search criteria.</p>
          </div>
        )}
      </div>

      <div className="groups-summary">
        <p>Showing {filteredGroups.length} of {groups.length} groups</p>
      </div>
    </div>
  );
};

export default Groups;
