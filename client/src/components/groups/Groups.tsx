import React, { useState, useEffect } from 'react';
import Pagination from '../common/Pagination';
import './Groups.css';

interface GroupMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  studentId: string;
}

interface Group {
  id: string;
  name: string;
  description: string;
  leaderId: string;
  leaderName: string;
  classId: string;
  className: string;
  memberCount: number;
  maxMembers: number;
  members: GroupMember[];
  createdAt: string;
}

interface Class {
  id: string;
  name: string;
  grade: number;
  stream: string;
  section: string;
}

interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  studentId: string;
}

const Groups: React.FC = () => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [deletingGroup, setDeletingGroup] = useState<Group | null>(null);

  // Form data states
  const [classes, setClasses] = useState<Class[]>([]);
  const [availableStudents, setAvailableStudents] = useState<Student[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [groupName, setGroupName] = useState('');
  const [maxMembers, setMaxMembers] = useState(4);
  const [groupDescription, setGroupDescription] = useState('');

  useEffect(() => {
    fetchGroups();
    fetchClasses();
  }, []);

  const fetchGroups = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/groups', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch groups');
      }

      const data = await response.json();
      setGroups(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/classes', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setClasses(data);
      }
    } catch (err) {
      console.error('Error fetching classes:', err);
    }
  };

  const fetchAvailableStudents = async (classId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/capacity/students-groups/${classId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        // Filter students who are not in any group
        const ungroupedStudents = data.students.filter((student: any) => !student.groupId);
        setAvailableStudents(ungroupedStudents);
      }
    } catch (err) {
      console.error('Error fetching available students:', err);
    }
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.className.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.leaderName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredGroups.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedGroups = filteredGroups.slice(startIndex, endIndex);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroup(expandedGroup === groupId ? null : groupId);
  };

  if (loading) {
    return (
      <div className="groups">
        <div className="groups-loading">
          <div className="loading-spinner"></div>
          <p>Loading groups...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="groups">
        <div className="error-message">
          <h3>Error Loading Groups</h3>
          <p>{error}</p>
          <button onClick={fetchGroups} className="btn btn-primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="groups">
      <div className="groups-header">
        <h1>Student Groups</h1>
        <p>Manage student groups and their members</p>
      </div>

      <div className="groups-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search groups by name, class, or leader..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="groups-stats">
          <div className="stat-item">
            <span className="stat-number">{groups.length}</span>
            <span className="stat-label">Total Groups</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {groups.reduce((sum, group) => sum + group.memberCount, 0)}
            </span>
            <span className="stat-label">Total Members</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {Math.round(groups.reduce((sum, group) => sum + group.memberCount, 0) / groups.length * 10) / 10 || 0}
            </span>
            <span className="stat-label">Avg Members/Group</span>
          </div>
        </div>
      </div>

      <div className="groups-table-container">
        <table className="groups-table">
          <thead>
            <tr>
              <th>Group Name</th>
              <th>Class</th>
              <th>Leader</th>
              <th>Members</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedGroups.map((group) => (
              <React.Fragment key={group.id}>
                <tr className="group-row">
                  <td className="group-name">
                    <div className="name-cell">
                      <span className="group-title">{group.name}</span>
                      {group.description && (
                        <span className="group-description">{group.description}</span>
                      )}
                    </div>
                  </td>
                  <td className="class-name">{group.className}</td>
                  <td className="leader-name">{group.leaderName}</td>
                  <td className="member-count">
                    <span className="count-badge">{group.memberCount}</span>
                  </td>
                  <td className="created-date">
                    {new Date(group.createdAt).toLocaleDateString()}
                  </td>
                  <td className="group-actions">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => toggleGroupExpansion(group.id)}
                    >
                      {expandedGroup === group.id ? 'Hide' : 'View'} Members
                    </button>
                  </td>
                </tr>

                {expandedGroup === group.id && (
                  <tr className="members-row">
                    <td colSpan={6}>
                      <div className="members-container">
                        <h4>Group Members</h4>
                        <div className="members-grid">
                          {group.members.map((member) => (
                            <div key={member.id} className="member-card">
                              <div className="member-info">
                                <span className="member-name">
                                  {member.firstName} {member.lastName}
                                </span>
                                <span className="member-id">ID: {member.studentId}</span>
                                <span className="member-email">{member.email}</span>
                              </div>
                              {member.id === group.leaderId && (
                                <span className="leader-badge">Leader</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>

        {filteredGroups.length === 0 && (
          <div className="no-groups">
            <h3>No groups found</h3>
            <p>No groups match your current search criteria.</p>
          </div>
        )}
      </div>

      {filteredGroups.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredGroups.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1);
          }}
          showItemsPerPage={true}
          showJumpToPage={true}
          itemsPerPageOptions={[5, 10, 20, 50]}
        />
      )}
    </div>
  );
};

export default Groups;
