import React, { useState, useEffect } from 'react';
import Pagination from '../common/Pagination';
import './Groups.css';

interface GroupMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  studentId: string;
}

interface Group {
  id: string;
  name: string;
  description: string;
  leaderId: string;
  leaderName: string;
  classId: string;
  className: string;
  memberCount: number;
  maxMembers: number;
  members: GroupMember[];
  createdAt: string;
}

interface Class {
  id: string;
  name: string;
  grade: number;
  stream: string;
  section: string;
}

interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  studentId: string;
}

const Groups: React.FC = () => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [deletingGroup, setDeletingGroup] = useState<Group | null>(null);

  // Form data states
  const [classes, setClasses] = useState<Class[]>([]);
  const [availableStudents, setAvailableStudents] = useState<Student[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [groupName, setGroupName] = useState('');
  const [maxMembers, setMaxMembers] = useState(4);
  const [groupDescription, setGroupDescription] = useState('');

  useEffect(() => {
    fetchGroups();
    fetchClasses();
  }, []);

  const fetchGroups = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/groups', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch groups');
      }

      const data = await response.json();
      setGroups(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/classes', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setClasses(data);
      }
    } catch (err) {
      console.error('Error fetching classes:', err);
    }
  };

  const fetchAvailableStudents = async (classId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/groups/available-students/${classId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAvailableStudents(data);
      }
    } catch (err) {
      console.error('Error fetching available students:', err);
    }
  };

  // Group Management Functions
  const handleCreateGroup = async () => {
    if (!selectedClass || !groupName || selectedStudents.length === 0) {
      alert('Please fill in all required fields and select at least one student');
      return;
    }

    try {
      const token = localStorage.getItem('token');

      // Create the group first
      const groupResponse = await fetch('/api/groups', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          groupName,
          classId: selectedClass,
          maxMembers,
          description: groupDescription
        }),
      });

      if (!groupResponse.ok) {
        const error = await groupResponse.json();
        throw new Error(error.error || 'Failed to create group');
      }

      const groupData = await groupResponse.json();
      const groupId = groupData.group.id;

      // Add selected students to the group
      for (let i = 0; i < selectedStudents.length; i++) {
        const studentId = selectedStudents[i];
        const role = i === 0 ? 'leader' : 'member'; // First selected student becomes leader

        await fetch(`/api/groups/${groupId}/members`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: studentId,
            role
          }),
        });
      }

      // Reset form and close modal
      resetCreateForm();
      setShowCreateModal(false);
      fetchGroups();
      alert('Group created successfully!');
    } catch (error) {
      console.error('Error creating group:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Failed to create group'}`);
    }
  };

  const handleEditGroup = async () => {
    if (!editingGroup || !groupName) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      const token = localStorage.getItem('token');

      // Update group details
      const response = await fetch(`/api/groups/${editingGroup.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          groupName,
          maxMembers,
          description: groupDescription
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update group');
      }

      setShowEditModal(false);
      setEditingGroup(null);
      fetchGroups();
      alert('Group updated successfully!');
    } catch (error) {
      console.error('Error updating group:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Failed to update group'}`);
    }
  };

  const handleDeleteGroup = async () => {
    if (!deletingGroup) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/groups/${deletingGroup.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete group');
      }

      setShowDeleteModal(false);
      setDeletingGroup(null);
      fetchGroups();
      alert(`Group deleted successfully! ${deletingGroup.memberCount} students have been released.`);
    } catch (error) {
      console.error('Error deleting group:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Failed to delete group'}`);
    }
  };

  // Utility Functions
  const resetCreateForm = () => {
    setSelectedClass('');
    setSelectedStudents([]);
    setGroupName('');
    setMaxMembers(4);
    setGroupDescription('');
    setAvailableStudents([]);
  };

  const openCreateModal = () => {
    resetCreateForm();
    setShowCreateModal(true);
  };

  const openEditModal = (group: Group) => {
    setEditingGroup(group);
    setGroupName(group.name);
    setMaxMembers(group.maxMembers);
    setGroupDescription(group.description || '');
    setShowEditModal(true);
  };

  const openDeleteModal = (group: Group) => {
    setDeletingGroup(group);
    setShowDeleteModal(true);
  };

  const handleClassChange = (classId: string) => {
    setSelectedClass(classId);
    setSelectedStudents([]);
    if (classId) {
      fetchAvailableStudents(classId);
    } else {
      setAvailableStudents([]);
    }
  };

  const handleStudentToggle = (studentId: string) => {
    setSelectedStudents(prev =>
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const fetchAvailableStudents = async (classId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/capacity/students-groups/${classId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        // Filter students who are not in any group
        const ungroupedStudents = data.students.filter((student: any) => !student.groupId);
        setAvailableStudents(ungroupedStudents);
      }
    } catch (err) {
      console.error('Error fetching available students:', err);
    }
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.className.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.leaderName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredGroups.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedGroups = filteredGroups.slice(startIndex, endIndex);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroup(expandedGroup === groupId ? null : groupId);
  };

  if (loading) {
    return (
      <div className="groups">
        <div className="groups-loading">
          <div className="loading-spinner"></div>
          <p>Loading groups...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="groups">
        <div className="error-message">
          <h3>Error Loading Groups</h3>
          <p>{error}</p>
          <button onClick={fetchGroups} className="btn btn-primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="groups">
      <div className="groups-header">
        <div className="header-content">
          <div>
            <h1>Student Groups</h1>
            <p>Manage student groups and their members</p>
          </div>
          <button
            className="btn btn-primary create-group-btn"
            onClick={openCreateModal}
          >
            Create New Group
          </button>
        </div>
      </div>

      <div className="groups-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search groups by name, class, or leader..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="groups-stats">
          <div className="stat-item">
            <span className="stat-number">{groups.length}</span>
            <span className="stat-label">Total Groups</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {groups.reduce((sum, group) => sum + group.memberCount, 0)}
            </span>
            <span className="stat-label">Total Members</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">
              {Math.round(groups.reduce((sum, group) => sum + group.memberCount, 0) / groups.length * 10) / 10 || 0}
            </span>
            <span className="stat-label">Avg Members/Group</span>
          </div>
        </div>
      </div>

      <div className="groups-table-container">
        <table className="groups-table">
          <thead>
            <tr>
              <th>Group Name</th>
              <th>Class</th>
              <th>Leader</th>
              <th>Members</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedGroups.map((group) => (
              <React.Fragment key={group.id}>
                <tr className="group-row">
                  <td className="group-name">
                    <div className="name-cell">
                      <span className="group-title">{group.name}</span>
                      {group.description && (
                        <span className="group-description">{group.description}</span>
                      )}
                    </div>
                  </td>
                  <td className="class-name">{group.className}</td>
                  <td className="leader-name">{group.leaderName}</td>
                  <td className="member-count">
                    <span className="count-badge">{group.memberCount}</span>
                  </td>
                  <td className="created-date">
                    {new Date(group.createdAt).toLocaleDateString()}
                  </td>
                  <td className="group-actions">
                    <div className="action-buttons">
                      <button
                        className="btn btn-sm btn-outline"
                        onClick={() => toggleGroupExpansion(group.id)}
                        title="View Members"
                      >
                        {expandedGroup === group.id ? 'Hide' : 'View'}
                      </button>
                      <button
                        className="btn btn-sm btn-secondary"
                        onClick={() => openEditModal(group)}
                        title="Edit Group"
                      >
                        Edit
                      </button>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => openDeleteModal(group)}
                        title="Delete Group"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>

                {expandedGroup === group.id && (
                  <tr className="members-row">
                    <td colSpan={6}>
                      <div className="members-container">
                        <h4>Group Members</h4>
                        <div className="members-grid">
                          {group.members.map((member) => (
                            <div key={member.id} className="member-card">
                              <div className="member-info">
                                <span className="member-name">
                                  {member.firstName} {member.lastName}
                                </span>
                                <span className="member-id">ID: {member.studentId}</span>
                                <span className="member-email">{member.email}</span>
                              </div>
                              {member.id === group.leaderId && (
                                <span className="leader-badge">Leader</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>

        {filteredGroups.length === 0 && (
          <div className="no-groups">
            <h3>No groups found</h3>
            <p>No groups match your current search criteria.</p>
          </div>
        )}
      </div>

      {filteredGroups.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredGroups.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1);
          }}
          showItemsPerPage={true}
          showJumpToPage={true}
          itemsPerPageOptions={[5, 10, 20, 50]}
        />
      )}

      {/* Create Group Modal */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Create New Group</h3>
              <button
                className="close-btn"
                onClick={() => setShowCreateModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Select Class *</label>
                <select
                  value={selectedClass}
                  onChange={(e) => handleClassChange(e.target.value)}
                  className="form-control"
                >
                  <option value="">Choose a class...</option>
                  {classes.map((cls) => (
                    <option key={cls.id} value={cls.id}>
                      {cls.name}
                    </option>
                  ))}
                </select>
              </div>

              {selectedClass && (
                <>
                  <div className="form-group">
                    <label>Group Name *</label>
                    <input
                      type="text"
                      value={groupName}
                      onChange={(e) => setGroupName(e.target.value)}
                      className="form-control"
                      placeholder="Enter group name"
                    />
                  </div>

                  <div className="form-group">
                    <label>Maximum Members</label>
                    <input
                      type="number"
                      value={maxMembers}
                      onChange={(e) => setMaxMembers(parseInt(e.target.value))}
                      className="form-control"
                      min="2"
                      max="6"
                    />
                  </div>

                  <div className="form-group">
                    <label>Description</label>
                    <textarea
                      value={groupDescription}
                      onChange={(e) => setGroupDescription(e.target.value)}
                      className="form-control"
                      placeholder="Enter group description"
                      rows={3}
                    />
                  </div>

                  <div className="form-group">
                    <label>Select Students * (First selected becomes leader)</label>
                    <div className="students-list">
                      {availableStudents.length === 0 ? (
                        <p className="no-students">No available students in this class</p>
                      ) : (
                        availableStudents.map((student) => (
                          <div key={student.id} className="student-item">
                            <label className="checkbox-label">
                              <input
                                type="checkbox"
                                checked={selectedStudents.includes(student.id)}
                                onChange={() => handleStudentToggle(student.id)}
                              />
                              <span className="student-info">
                                <span className="student-name">
                                  {student.firstName} {student.lastName}
                                </span>
                                <span className="student-id">ID: {student.studentId}</span>
                                {selectedStudents.indexOf(student.id) === 0 &&
                                 selectedStudents.includes(student.id) && (
                                  <span className="leader-badge">Leader</span>
                                )}
                              </span>
                            </label>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </>
              )}

              <div className="modal-actions">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleCreateGroup}
                  className="btn btn-primary"
                  disabled={!selectedClass || !groupName || selectedStudents.length === 0}
                >
                  Create Group
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Group Modal */}
      {showEditModal && editingGroup && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Edit Group</h3>
              <button
                className="close-btn"
                onClick={() => setShowEditModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Class</label>
                <input
                  type="text"
                  value={editingGroup.className}
                  className="form-control"
                  disabled
                />
              </div>

              <div className="form-group">
                <label>Group Name *</label>
                <input
                  type="text"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  className="form-control"
                  placeholder="Enter group name"
                />
              </div>

              <div className="form-group">
                <label>Maximum Members</label>
                <input
                  type="number"
                  value={maxMembers}
                  onChange={(e) => setMaxMembers(parseInt(e.target.value))}
                  className="form-control"
                  min={editingGroup.memberCount}
                  max="6"
                />
                <small className="form-text">
                  Cannot be less than current member count ({editingGroup.memberCount})
                </small>
              </div>

              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={groupDescription}
                  onChange={(e) => setGroupDescription(e.target.value)}
                  className="form-control"
                  placeholder="Enter group description"
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label>Current Members ({editingGroup.memberCount})</label>
                <div className="current-members-list">
                  {editingGroup.members.map((member) => (
                    <div key={member.id} className="member-item">
                      <span className="member-info">
                        <span className="member-name">
                          {member.firstName} {member.lastName}
                        </span>
                        <span className="member-id">ID: {member.studentId}</span>
                        {member.id === editingGroup.leaderId && (
                          <span className="leader-badge">Leader</span>
                        )}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="modal-actions">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleEditGroup}
                  className="btn btn-primary"
                  disabled={!groupName}
                >
                  Update Group
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Group Modal */}
      {showDeleteModal && deletingGroup && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Delete Group</h3>
              <button
                className="close-btn"
                onClick={() => setShowDeleteModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="delete-confirmation">
                <p>Are you sure you want to delete the group <strong>"{deletingGroup.name}"</strong>?</p>
                <div className="delete-details">
                  <p><strong>Class:</strong> {deletingGroup.className}</p>
                  <p><strong>Members:</strong> {deletingGroup.memberCount} students</p>
                  <p className="warning-text">
                    ⚠️ This action cannot be undone. All {deletingGroup.memberCount} students will be removed from the group and become available for other group assignments.
                  </p>
                </div>
              </div>

              <div className="modal-actions">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDeleteGroup}
                  className="btn btn-danger"
                >
                  Delete Group
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Groups;
