import React, { useState, useEffect } from 'react';
import { submissionsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import Pagination from '../common/Pagination';
import './Submissions.css';

interface Submission {
  id: string;
  scheduleTitle: string;
  labName: string;
  className: string;
  submissionType: 'file' | 'text' | 'mixed';
  status: 'pending' | 'submitted' | 'late' | 'graded';
  submittedAt?: string;
  dueDate: string;
  files: string[];
  textContent?: string;
  grade?: {
    score: number;
    maxScore: number;
    feedback: string;
  };
  groupName?: string;
  studentName?: string;
}

const Submissions: React.FC = () => {
  const { user } = useAuth();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'submitted' | 'graded'>('all');
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [classFilter, setClassFilter] = useState('all');
  const [classes, setClasses] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Demo data
  const demoSubmissions: Submission[] = [
    {
      id: '1',
      scheduleTitle: 'Web Development Practical',
      labName: 'Lab 1',
      className: '11th Non-Medical A',
      submissionType: 'mixed',
      status: 'submitted',
      submittedAt: '2024-01-14T16:30:00Z',
      dueDate: '2024-01-15T23:59:00Z',
      files: ['index.html', 'style.css', 'script.js'],
      textContent: 'This is my web development project implementing a responsive portfolio website.',
      groupName: 'Group Alpha',
      studentName: user?.role === 'instructor' ? 'John Doe' : undefined
    },
    {
      id: '2',
      scheduleTitle: 'Database Design Lab',
      labName: 'Lab 2',
      className: '12th Non-Medical A',
      submissionType: 'file',
      status: 'graded',
      submittedAt: '2024-01-10T14:20:00Z',
      dueDate: '2024-01-11T23:59:00Z',
      files: ['database_schema.sql', 'queries.sql'],
      grade: {
        score: 85,
        maxScore: 100,
        feedback: 'Good work on the database design. Consider adding more indexes for better performance.'
      },
      groupName: 'Group Beta',
      studentName: user?.role === 'instructor' ? 'Jane Smith' : undefined
    },
    {
      id: '3',
      scheduleTitle: 'Python Programming',
      labName: 'Lab 1',
      className: '11th Non-Medical B',
      submissionType: 'text',
      status: 'pending',
      dueDate: '2024-01-20T23:59:00Z',
      files: [],
      textContent: '',
      groupName: 'Group Gamma',
      studentName: user?.role === 'instructor' ? 'Mike Johnson' : undefined
    }
  ];

  useEffect(() => {
    const fetchSubmissions = async () => {
      try {
        setLoading(true);
        const response = await submissionsAPI.getSubmissions();
        // The API returns data in response.data.submissions format
        const submissionsData = response.data.submissions || response.data;

        // Transform the API data to match our interface
        const transformedSubmissions = Array.isArray(submissionsData) ? submissionsData.map((submission: any) => ({
          id: submission.id,
          scheduleTitle: submission.schedule_title,
          labName: submission.lab_name || 'Lab',
          className: submission.class_name || 'Class',
          submissionType: submission.submission_type,
          status: submission.status,
          submittedAt: submission.submitted_at,
          dueDate: submission.scheduled_date || submission.due_date,
          files: Array.isArray(submission.file_paths) ? submission.file_paths :
                 (typeof submission.file_paths === 'string' ? JSON.parse(submission.file_paths) : []),
          textContent: submission.text_content || submission.content,
          grade: submission.score ? {
            score: parseFloat(submission.score),
            maxScore: parseFloat(submission.max_score || '100'),
            feedback: submission.feedback || ''
          } : undefined,
          groupName: submission.group_name,
          studentName: user?.role === 'instructor' ? `${submission.first_name} ${submission.last_name}` : undefined
        })) : [];

        setSubmissions(transformedSubmissions);
      } catch (error) {
        console.error('Error fetching submissions:', error);
        console.warn('Using demo data for submissions');
        setSubmissions(demoSubmissions);
      } finally {
        setLoading(false);
      }
    };

    fetchSubmissions();
    fetchClasses();
  }, [user]);

  const fetchClasses = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/classes', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setClasses(data.classes || []);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  // Safety check to ensure submissions is an array
  const submissionsArray = Array.isArray(submissions) ? submissions : [];

  const filteredSubmissions = submissionsArray.filter(submission => {
    const matchesFilter = filter === 'all' || submission.status === filter;
    const matchesSearch = submission.scheduleTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         submission.textContent?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         submission.studentName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         submission.groupName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = classFilter === 'all' || submission.className === classFilter;

    return matchesFilter && matchesSearch && matchesClass;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredSubmissions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedSubmissions = filteredSubmissions.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filter, searchTerm, classFilter]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'submitted': return '#3498db';
      case 'late': return '#e74c3c';
      case 'graded': return '#27ae60';
      default: return '#95a5a6';
    }
  };

  const isOverdue = (dueDate: string, status: string) => {
    return status === 'pending' && new Date() > new Date(dueDate);
  };

  if (loading) {
    return (
      <div className="submissions-loading">
        <div className="loading-spinner"></div>
        <p>Loading submissions...</p>
      </div>
    );
  }

  return (
    <div className="submissions">
      <div className="submissions-header">
        <h1>Submissions</h1>
        <p>{user?.role === 'instructor' ? 'Review and grade student submissions' : 'Manage your assignment submissions'}</p>
      </div>

      <div className="submissions-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search submissions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-section">
          <select
            value={classFilter}
            onChange={(e) => setClassFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Classes</option>
            {classes.map(cls => (
              <option key={cls.id} value={cls.name}>{cls.name}</option>
            ))}
          </select>

          <div className="filter-tabs">
            <button
              className={`filter-tab ${filter === 'all' ? 'active' : ''}`}
              onClick={() => setFilter('all')}
            >
              All
            </button>
            <button
              className={`filter-tab ${filter === 'pending' ? 'active' : ''}`}
              onClick={() => setFilter('pending')}
            >
              Pending
            </button>
            <button
              className={`filter-tab ${filter === 'submitted' ? 'active' : ''}`}
              onClick={() => setFilter('submitted')}
            >
              Submitted
            </button>
            <button
              className={`filter-tab ${filter === 'graded' ? 'active' : ''}`}
              onClick={() => setFilter('graded')}
            >
              Graded
            </button>
          </div>
        </div>
      </div>

      <div className="submissions-table-container">
        <table className="submissions-table">
          <thead>
            <tr>
              <th>Assignment</th>
              <th>Lab</th>
              <th>Class</th>
              {user?.role === 'instructor' && <th>Student/Group</th>}
              <th>Type</th>
              <th>Due Date</th>
              <th>Submitted</th>
              <th>Status</th>
              <th>Grade</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedSubmissions.length === 0 ? (
              <tr>
                <td colSpan={user?.role === 'instructor' ? 10 : 9} className="no-submissions">
                  <div className="no-data">
                    <h3>No submissions found</h3>
                    <p>There are no submissions matching your current filters.</p>
                  </div>
                </td>
              </tr>
            ) : (
              paginatedSubmissions.map((submission) => (
                <tr key={submission.id} className="submission-row">
                  <td className="submission-title">
                    <div className="title-cell">
                      <span className="title">{submission.scheduleTitle}</span>
                      {submission.textContent && (
                        <span className="content-preview">
                          {submission.textContent.substring(0, 50)}...
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="lab-name">{submission.labName}</td>
                  <td className="class-name">{submission.className}</td>
                  {user?.role === 'instructor' && (
                    <td className="student-info">
                      {submission.groupName ? (
                        <span className="group-name">👥 {submission.groupName}</span>
                      ) : (
                        <span className="student-name">👤 {submission.studentName}</span>
                      )}
                    </td>
                  )}
                  <td>
                    <span className={`type-badge ${submission.submissionType}`}>
                      {submission.submissionType}
                    </span>
                  </td>
                  <td className="due-date">
                    {formatDate(submission.dueDate)}
                  </td>
                  <td className="submitted-date">
                    {submission.submittedAt ? formatDate(submission.submittedAt) : '-'}
                  </td>
                  <td>
                    <span
                      className={`status-badge ${isOverdue(submission.dueDate, submission.status) ? 'overdue' : ''}`}
                      style={{ backgroundColor: getStatusColor(isOverdue(submission.dueDate, submission.status) ? 'late' : submission.status) }}
                    >
                      {isOverdue(submission.dueDate, submission.status) ? 'OVERDUE' : submission.status.toUpperCase()}
                    </span>
                  </td>
                  <td className="grade-cell">
                    {submission.grade ? (
                      <div className="grade-info">
                        <span className="grade-score">
                          {submission.grade.score}/{submission.grade.maxScore}
                        </span>
                        <span className="grade-percentage">
                          ({Math.round((submission.grade.score / submission.grade.maxScore) * 100)}%)
                        </span>
                      </div>
                    ) : (
                      <span className="no-grade">-</span>
                    )}
                  </td>
                  <td className="submission-actions">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => setSelectedSubmission(submission)}
                    >
                      View
                    </button>
                    {user?.role === 'student' && submission.status === 'pending' && (
                      <button className="btn btn-sm btn-primary">Submit</button>
                    )}
                    {user?.role === 'instructor' && submission.status === 'submitted' && (
                      <button className="btn btn-sm btn-secondary">Grade</button>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {filteredSubmissions.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredSubmissions.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1);
          }}
          showItemsPerPage={true}
          showJumpToPage={true}
          itemsPerPageOptions={[5, 10, 20, 50]}
        />
      )}
    </div>
  );
};

export default Submissions;
