import React, { useState, useEffect } from 'react';
import { submissionsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import './Submissions.css';

interface Submission {
  id: string;
  scheduleTitle: string;
  labName: string;
  className: string;
  submissionType: 'file' | 'text' | 'mixed';
  status: 'pending' | 'submitted' | 'late' | 'graded';
  submittedAt?: string;
  dueDate: string;
  files: string[];
  textContent?: string;
  grade?: {
    score: number;
    maxScore: number;
    feedback: string;
  };
  groupName?: string;
  studentName?: string;
}

const Submissions: React.FC = () => {
  const { user } = useAuth();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'submitted' | 'graded'>('all');
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);

  // Demo data
  const demoSubmissions: Submission[] = [
    {
      id: '1',
      scheduleTitle: 'Web Development Practical',
      labName: 'Lab 1',
      className: '11th Non-Medical A',
      submissionType: 'mixed',
      status: 'submitted',
      submittedAt: '2024-01-14T16:30:00Z',
      dueDate: '2024-01-15T23:59:00Z',
      files: ['index.html', 'style.css', 'script.js'],
      textContent: 'This is my web development project implementing a responsive portfolio website.',
      groupName: 'Group Alpha',
      studentName: user?.role === 'instructor' ? 'John Doe' : undefined
    },
    {
      id: '2',
      scheduleTitle: 'Database Design Lab',
      labName: 'Lab 2',
      className: '12th Non-Medical A',
      submissionType: 'file',
      status: 'graded',
      submittedAt: '2024-01-10T14:20:00Z',
      dueDate: '2024-01-11T23:59:00Z',
      files: ['database_schema.sql', 'queries.sql'],
      grade: {
        score: 85,
        maxScore: 100,
        feedback: 'Good work on the database design. Consider adding more indexes for better performance.'
      },
      groupName: 'Group Beta',
      studentName: user?.role === 'instructor' ? 'Jane Smith' : undefined
    },
    {
      id: '3',
      scheduleTitle: 'Python Programming',
      labName: 'Lab 1',
      className: '11th Non-Medical B',
      submissionType: 'text',
      status: 'pending',
      dueDate: '2024-01-20T23:59:00Z',
      files: [],
      textContent: '',
      groupName: 'Group Gamma',
      studentName: user?.role === 'instructor' ? 'Mike Johnson' : undefined
    }
  ];

  useEffect(() => {
    const fetchSubmissions = async () => {
      try {
        setLoading(true);
        const response = await submissionsAPI.getSubmissions();
        // The API returns data in response.data.submissions format
        const submissionsData = response.data.submissions || response.data;

        // Transform the API data to match our interface
        const transformedSubmissions = Array.isArray(submissionsData) ? submissionsData.map((submission: any) => ({
          id: submission.id,
          scheduleTitle: submission.schedule_title,
          labName: submission.lab_name || 'Lab',
          className: submission.class_name || 'Class',
          submissionType: submission.submission_type,
          status: submission.status,
          submittedAt: submission.submitted_at,
          dueDate: submission.scheduled_date || submission.due_date,
          files: Array.isArray(submission.file_paths) ? submission.file_paths :
                 (typeof submission.file_paths === 'string' ? JSON.parse(submission.file_paths) : []),
          textContent: submission.text_content || submission.content,
          grade: submission.score ? {
            score: parseFloat(submission.score),
            maxScore: parseFloat(submission.max_score || '100'),
            feedback: submission.feedback || ''
          } : undefined,
          groupName: submission.group_name,
          studentName: user?.role === 'instructor' ? `${submission.first_name} ${submission.last_name}` : undefined
        })) : [];

        setSubmissions(transformedSubmissions);
      } catch (error) {
        console.error('Error fetching submissions:', error);
        console.warn('Using demo data for submissions');
        setSubmissions(demoSubmissions);
      } finally {
        setLoading(false);
      }
    };

    fetchSubmissions();
  }, [user]);

  // Safety check to ensure submissions is an array
  const submissionsArray = Array.isArray(submissions) ? submissions : [];

  const filteredSubmissions = submissionsArray.filter(submission => {
    if (filter === 'all') return true;
    return submission.status === filter;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'submitted': return '#3498db';
      case 'late': return '#e74c3c';
      case 'graded': return '#27ae60';
      default: return '#95a5a6';
    }
  };

  const isOverdue = (dueDate: string, status: string) => {
    return status === 'pending' && new Date() > new Date(dueDate);
  };

  if (loading) {
    return (
      <div className="submissions-loading">
        <div className="loading-spinner"></div>
        <p>Loading submissions...</p>
      </div>
    );
  }

  return (
    <div className="submissions">
      <div className="submissions-header">
        <h1>Submissions</h1>
        <p>{user?.role === 'instructor' ? 'Review and grade student submissions' : 'Manage your assignment submissions'}</p>
      </div>

      <div className="submissions-controls">
        <div className="filter-tabs">
          <button
            className={`filter-tab ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          <button
            className={`filter-tab ${filter === 'pending' ? 'active' : ''}`}
            onClick={() => setFilter('pending')}
          >
            Pending
          </button>
          <button
            className={`filter-tab ${filter === 'submitted' ? 'active' : ''}`}
            onClick={() => setFilter('submitted')}
          >
            Submitted
          </button>
          <button
            className={`filter-tab ${filter === 'graded' ? 'active' : ''}`}
            onClick={() => setFilter('graded')}
          >
            Graded
          </button>
        </div>
      </div>

      <div className="submissions-grid">
        {filteredSubmissions.map((submission) => (
          <div key={submission.id} className="submission-card">
            <div className="submission-header">
              <h3>{submission.scheduleTitle}</h3>
              <div
                className={`submission-status ${isOverdue(submission.dueDate, submission.status) ? 'overdue' : ''}`}
                style={{ backgroundColor: getStatusColor(isOverdue(submission.dueDate, submission.status) ? 'late' : submission.status) }}
              >
                {isOverdue(submission.dueDate, submission.status) ? 'OVERDUE' : submission.status.toUpperCase()}
              </div>
            </div>

            <div className="submission-details">
              <div className="detail-item">
                <strong>Lab:</strong> {submission.labName}
              </div>
              <div className="detail-item">
                <strong>Class:</strong> {submission.className}
              </div>
              {user?.role === 'instructor' && submission.studentName && (
                <div className="detail-item">
                  <strong>Student:</strong> {submission.studentName}
                </div>
              )}
              {submission.groupName && (
                <div className="detail-item">
                  <strong>Group:</strong> {submission.groupName}
                </div>
              )}
              <div className="detail-item">
                <strong>Type:</strong> {submission.submissionType}
              </div>
              <div className="detail-item">
                <strong>Due Date:</strong> {formatDate(submission.dueDate)}
              </div>
              {submission.submittedAt && (
                <div className="detail-item">
                  <strong>Submitted:</strong> {formatDate(submission.submittedAt)}
                </div>
              )}
            </div>

            {submission.files.length > 0 && (
              <div className="submission-files">
                <strong>Files:</strong>
                <div className="file-list">
                  {submission.files.map((file, index) => (
                    <span key={index} className="file-tag">{file}</span>
                  ))}
                </div>
              </div>
            )}

            {submission.textContent && (
              <div className="submission-text">
                <strong>Text Submission:</strong>
                <p>{submission.textContent.substring(0, 100)}...</p>
              </div>
            )}

            {submission.grade && (
              <div className="submission-grade">
                <div className="grade-score">
                  <span className="score">{submission.grade.score}</span>
                  <span className="max-score">/{submission.grade.maxScore}</span>
                </div>
                <div className="grade-percentage">
                  {Math.round((submission.grade.score / submission.grade.maxScore) * 100)}%
                </div>
              </div>
            )}

            <div className="submission-actions">
              <button
                className="btn btn-outline"
                onClick={() => setSelectedSubmission(submission)}
              >
                View Details
              </button>
              {user?.role === 'student' && submission.status === 'pending' && (
                <button className="btn btn-primary">Submit Work</button>
              )}
              {user?.role === 'instructor' && submission.status === 'submitted' && (
                <button className="btn btn-secondary">Grade</button>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredSubmissions.length === 0 && (
        <div className="no-submissions">
          <h3>No submissions found</h3>
          <p>There are no submissions matching your current filter.</p>
        </div>
      )}
    </div>
  );
};

export default Submissions;
