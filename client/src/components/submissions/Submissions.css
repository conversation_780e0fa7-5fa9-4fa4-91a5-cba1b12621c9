.submissions {
  padding: 2rem;
  width: 100%;
  margin: 0;
}

.submissions-header {
  margin-bottom: 2rem;
  text-align: center;
}

.submissions-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.submissions-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.submissions-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.submissions-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e1e8ed;
  background: white;
  color: #7f8c8d;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-tab.active {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
}

.submissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.submission-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.submission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.submission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.submission-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  flex: 1;
}

.submission-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.submission-status.overdue {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.submission-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
  font-size: 0.9rem;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item strong {
  color: #2c3e50;
  font-weight: 600;
}

.submission-files {
  margin-bottom: 1.5rem;
}

.submission-files strong {
  color: #2c3e50;
  display: block;
  margin-bottom: 0.5rem;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.file-tag {
  background-color: #e8f4fd;
  color: #1565c0;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.submission-text {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.submission-text strong {
  color: #2c3e50;
  display: block;
  margin-bottom: 0.5rem;
}

.submission-text p {
  color: #5a6c7d;
  margin: 0;
  line-height: 1.5;
  font-style: italic;
}

.submission-grade {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 8px;
  color: white;
}

.grade-score {
  display: flex;
  align-items: baseline;
}

.score {
  font-size: 2rem;
  font-weight: 700;
}

.max-score {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-left: 0.25rem;
}

.grade-percentage {
  font-size: 1.5rem;
  font-weight: 700;
}

.submission-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex: 1;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background-color: #3498db;
  color: white;
  transform: translateY(-1px);
}

.no-submissions {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.no-submissions h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .submissions {
    padding: 1rem;
  }
  
  .submissions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .submission-card {
    padding: 1rem;
  }
  
  .submission-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .submission-actions {
    flex-direction: column;
  }
  
  .filter-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .submissions-header h1 {
    font-size: 2rem;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-start;
  }
  
  .submission-grade {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
