.submissions {
  padding: 2rem;
  width: 100%;
  margin: 0;
}

.submissions-header {
  margin-bottom: 2rem;
  text-align: center;
}

.submissions-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.submissions-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.submissions-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.submissions-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-section {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-section {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e1e8ed;
  background: white;
  color: #7f8c8d;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-tab.active {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
}

/* Table Styles */
.submissions-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.submissions-table {
  width: 100%;
  border-collapse: collapse;
}

.submissions-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
  white-space: nowrap;
}

.submissions-table td {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: middle;
}

.submission-row:hover {
  background: #f8f9fa;
}

.submission-title .title-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.submission-title .title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.submission-title .content-preview {
  font-size: 0.875rem;
  color: #7f8c8d;
  line-height: 1.4;
}

.student-info {
  font-size: 0.9rem;
}

.group-name {
  color: #27ae60;
  font-weight: 500;
}

.student-name {
  color: #3498db;
  font-weight: 500;
}

.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.file {
  background: #e8f5e8;
  color: #27ae60;
}

.type-badge.text {
  background: #e8f4fd;
  color: #3498db;
}

.type-badge.mixed {
  background: #fef9e7;
  color: #f39c12;
}

.due-date, .submitted-date {
  font-size: 0.9rem;
  color: #2c3e50;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.overdue {
  background-color: #e74c3c !important;
}

.grade-cell {
  font-size: 0.9rem;
}

.grade-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.grade-score {
  font-weight: 600;
  color: #2c3e50;
}

.grade-percentage {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.no-grade {
  color: #bdc3c7;
  font-style: italic;
}

.submission-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.no-data {
  text-align: center;
  padding: 3rem;
}

.no-data h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.no-data p {
  color: #7f8c8d;
}

.submissions-summary {
  text-align: center;
  padding: 1rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.submissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.submission-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.submission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.submission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.submission-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  flex: 1;
}

.submission-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.submission-status.overdue {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.submission-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
  font-size: 0.9rem;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item strong {
  color: #2c3e50;
  font-weight: 600;
}

.submission-files {
  margin-bottom: 1.5rem;
}

.submission-files strong {
  color: #2c3e50;
  display: block;
  margin-bottom: 0.5rem;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.file-tag {
  background-color: #e8f4fd;
  color: #1565c0;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.submission-text {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.submission-text strong {
  color: #2c3e50;
  display: block;
  margin-bottom: 0.5rem;
}

.submission-text p {
  color: #5a6c7d;
  margin: 0;
  line-height: 1.5;
  font-style: italic;
}

.submission-grade {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 8px;
  color: white;
}

.grade-score {
  display: flex;
  align-items: baseline;
}

.score {
  font-size: 2rem;
  font-weight: 700;
}

.max-score {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-left: 0.25rem;
}

.grade-percentage {
  font-size: 1.5rem;
  font-weight: 700;
}

.submission-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex: 1;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background-color: #3498db;
  color: white;
  transform: translateY(-1px);
}

.no-submissions {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.no-submissions h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .submissions-table th,
  .submissions-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .submission-title .content-preview {
    display: none;
  }
}

@media (max-width: 768px) {
  .submissions {
    padding: 1rem;
  }

  .submissions-controls {
    padding: 1rem;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-tabs {
    justify-content: center;
    flex-wrap: wrap;
  }

  .submissions-table-container {
    overflow-x: auto;
  }

  .submissions-table {
    min-width: 800px;
  }

  .submission-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .submissions-header h1 {
    font-size: 2rem;
  }

  .filter-tabs {
    flex-wrap: wrap;
  }

  .submissions-table {
    min-width: 600px;
  }

  .submissions-table th,
  .submissions-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.8rem;
  }
}
