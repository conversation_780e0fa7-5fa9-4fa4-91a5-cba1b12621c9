.assignments {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.assignments-header {
  text-align: center;
  margin-bottom: 2rem;
}

.assignments-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.assignments-header p {
  font-size: 1.1rem;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto;
}

.assignments-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.assignments-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-section {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-section {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e1e8ed;
  background: white;
  color: #7f8c8d;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-tab.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

.assignments-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.assignments-table {
  width: 100%;
  border-collapse: collapse;
}

.assignments-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
  white-space: nowrap;
}

.assignments-table td {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: middle;
}

.assignment-row:hover {
  background: #f8f9fa;
}

.assignment-title .title-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.assignment-title .title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.assignment-title .description {
  font-size: 0.875rem;
  color: #7f8c8d;
  line-height: 1.4;
}

.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.group {
  background: #e8f5e8;
  color: #27ae60;
}

.type-badge.individual {
  background: #e8f4fd;
  color: #3498db;
}

.assigned-to {
  font-size: 0.9rem;
}

.group-assignment {
  color: #27ae60;
  font-weight: 500;
}

.individual-assignment {
  color: #3498db;
  font-weight: 500;
}

.computer-info {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.scheduled-date {
  font-size: 0.9rem;
  color: #2c3e50;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.assignment-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-outline {
  background: white;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.no-assignments {
  text-align: center;
  padding: 3rem;
}

.no-data h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.no-data p {
  color: #7f8c8d;
}

.assignments-summary {
  text-align: center;
  padding: 1rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .assignments-table th,
  .assignments-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .assignment-title .description {
    display: none;
  }
}

@media (max-width: 768px) {
  .assignments {
    padding: 1rem;
  }
  
  .assignments-controls {
    padding: 1rem;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-tabs {
    justify-content: center;
  }
  
  .assignments-table-container {
    overflow-x: auto;
  }
  
  .assignments-table {
    min-width: 800px;
  }
  
  .assignment-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .assignments-header h1 {
    font-size: 2rem;
  }
  
  .filter-tabs {
    flex-wrap: wrap;
  }
  
  .assignments-table {
    min-width: 600px;
  }
}

/* Admin Interface Styles */
.admin-interface {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.admin-controls h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.25rem;
}

.admin-selectors {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.selector-group {
  flex: 1;
}

.selector-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
}

.form-select:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.admin-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background: #f8f9fa;
}

.tab-button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* Seats Management */
.seats-management h4,
.computers-management h4 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.seats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.seat-item {
  padding: 1rem;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  text-align: center;
  transition: all 0.2s ease;
}

.seat-item.available {
  background: #f8f9fa;
  border-color: #6c757d;
}

.seat-item.assigned {
  background: #d4edda;
  border-color: #28a745;
}

.seat-number {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #495057;
}

.seat-assignment {
  font-size: 0.875rem;
}

.student-name {
  font-weight: 500;
  color: #28a745;
}

.student-id {
  color: #6c757d;
  font-size: 0.75rem;
}

/* Computers Management */
.computers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.computer-item {
  padding: 1rem;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.computer-item.available {
  background: #f8f9fa;
  border-color: #6c757d;
}

.computer-item.assigned {
  background: #d4edda;
  border-color: #28a745;
}

.computer-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #495057;
}

.computer-status {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.computer-assignment {
  font-size: 0.875rem;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
}

.assignment-info {
  font-weight: 500;
  color: #28a745;
}
