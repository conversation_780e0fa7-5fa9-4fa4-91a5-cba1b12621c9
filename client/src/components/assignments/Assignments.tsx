import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import Pagination from '../common/Pagination';
import './Assignments.css';

interface Assignment {
  id: string;
  scheduleId: string;
  scheduleTitle: string;
  description: string;
  labName: string;
  className: string;
  instructorName: string;
  scheduledDate: string;
  durationMinutes: number;
  status: string;
  assignmentType: 'group' | 'individual';
  groupId?: string;
  groupName?: string;
  userId?: string;
  studentName?: string;
  computerId?: string;
  computerName?: string;
  assignedAt: string;
}

const Assignments: React.FC = () => {
  const { user } = useAuth();
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'group' | 'individual'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [classFilter, setClassFilter] = useState('all');
  const [classes, setClasses] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    fetchAssignments();
    fetchClasses();
  }, []);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('/api/assignments', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments || []);
      } else {
        console.error('Failed to fetch assignments');
        setAssignments([]);
      }
    } catch (error) {
      console.error('Error fetching assignments:', error);
      setAssignments([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/classes', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setClasses(data.classes || []);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesFilter = filter === 'all' || assignment.assignmentType === filter;
    const matchesSearch = assignment.scheduleTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.labName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = classFilter === 'all' || assignment.className === classFilter;

    return matchesFilter && matchesSearch && matchesClass;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredAssignments.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedAssignments = filteredAssignments.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filter, searchTerm, classFilter]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'scheduled': return '#3498db';
      case 'in_progress': return '#f39c12';
      case 'completed': return '#27ae60';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="assignments-loading">
        <div className="loading-spinner"></div>
        <p>Loading assignments...</p>
      </div>
    );
  }

  return (
    <div className="assignments">
      <div className="assignments-header">
        <h1>Assignments</h1>
        <p>
          {user?.role === 'instructor' 
            ? 'Manage practical assignments for students and groups' 
            : 'View your assigned practicals and lab sessions'
          }
        </p>
      </div>

      <div className="assignments-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search assignments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-section">
          <select
            value={classFilter}
            onChange={(e) => setClassFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Classes</option>
            {classes.map(cls => (
              <option key={cls.id} value={cls.name}>{cls.name}</option>
            ))}
          </select>

          <div className="filter-tabs">
            <button
              className={`filter-tab ${filter === 'all' ? 'active' : ''}`}
              onClick={() => setFilter('all')}
            >
              All
            </button>
            <button
              className={`filter-tab ${filter === 'group' ? 'active' : ''}`}
              onClick={() => setFilter('group')}
            >
              Group
            </button>
            <button
              className={`filter-tab ${filter === 'individual' ? 'active' : ''}`}
              onClick={() => setFilter('individual')}
            >
              Individual
            </button>
          </div>
        </div>
      </div>

      <div className="assignments-table-container">
        <table className="assignments-table">
          <thead>
            <tr>
              <th>Assignment</th>
              <th>Lab</th>
              <th>Class</th>
              <th>Type</th>
              <th>Assigned To</th>
              <th>Computer</th>
              <th>Scheduled Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedAssignments.length === 0 ? (
              <tr>
                <td colSpan={9} className="no-assignments">
                  <div className="no-data">
                    <h3>No assignments found</h3>
                    <p>No assignments match your current filters.</p>
                  </div>
                </td>
              </tr>
            ) : (
              paginatedAssignments.map((assignment) => (
                <tr key={assignment.id} className="assignment-row">
                  <td className="assignment-title">
                    <div className="title-cell">
                      <span className="title">{assignment.scheduleTitle}</span>
                      <span className="description">{assignment.description}</span>
                    </div>
                  </td>
                  <td className="lab-name">{assignment.labName}</td>
                  <td className="class-name">{assignment.className}</td>
                  <td>
                    <span className={`type-badge ${assignment.assignmentType}`}>
                      {assignment.assignmentType}
                    </span>
                  </td>
                  <td className="assigned-to">
                    {assignment.assignmentType === 'group' ? (
                      <span className="group-assignment">
                        👥 {assignment.groupName}
                      </span>
                    ) : (
                      <span className="individual-assignment">
                        👤 {assignment.studentName}
                      </span>
                    )}
                  </td>
                  <td className="computer-info">
                    {assignment.computerName || 'Not assigned'}
                  </td>
                  <td className="scheduled-date">
                    {formatDate(assignment.scheduledDate)}
                  </td>
                  <td>
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(assignment.status) }}
                    >
                      {assignment.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="assignment-actions">
                    <button className="btn btn-sm btn-outline">
                      View Details
                    </button>
                    {user?.role === 'instructor' && (
                      <button className="btn btn-sm btn-secondary">
                        Manage
                      </button>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {filteredAssignments.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredAssignments.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={(newItemsPerPage) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1);
          }}
          showItemsPerPage={true}
          showJumpToPage={true}
          itemsPerPageOptions={[5, 10, 20, 50]}
        />
      )}
    </div>
  );
};

export default Assignments;
