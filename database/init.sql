-- LabSyncPro Database Initialization Script
-- This script creates the database and all required tables

-- <PERSON>reate database (run this separately if needed)
-- CREATE DATABASE labsyncpro;

-- Connect to the database
\c labsyncpro;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> ENUM types
CREATE TYPE user_role AS ENUM ('student', 'instructor', 'admin');
CREATE TYPE schedule_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
CREATE TYPE submission_status AS ENUM ('pending', 'submitted', 'late', 'graded');
CREATE TYPE submission_type AS ENUM ('file', 'text', 'mixed');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'student',
    student_id VARCHAR(8) UNIQUE,
    profile_data JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_student_id CHECK (
        (role = 'student' AND student_id IS NOT NULL AND student_id ~ '^[0-9]{8}$') OR
        (role != 'student')
    )
);

-- Classes table
CREATE TABLE classes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    grade INTEGER NOT NULL CHECK (grade IN (11, 12)),
    stream VARCHAR(50) NOT NULL CHECK (stream IN ('Non-Medical', 'Medical', 'Commerce')),
    capacity INTEGER NOT NULL DEFAULT 50,
    instructor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(name, grade, stream)
);

-- Labs table
CREATE TABLE labs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    total_computers INTEGER NOT NULL,
    total_seats INTEGER NOT NULL,
    location VARCHAR(255),
    equipment JSONB DEFAULT '[]',
    availability_schedule JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT positive_capacity CHECK (total_computers > 0 AND total_seats > 0)
);

-- Groups table
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
    leader_id UUID REFERENCES users(id) ON DELETE SET NULL,
    max_members INTEGER NOT NULL DEFAULT 4 CHECK (max_members BETWEEN 3 AND 4),
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(name, class_id)
);

-- Group members table
CREATE TABLE group_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(group_id, user_id)
);

-- Schedules table
CREATE TABLE schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    lab_id UUID NOT NULL REFERENCES labs(id) ON DELETE CASCADE,
    instructor_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    class_id UUID REFERENCES classes(id) ON DELETE SET NULL,
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER NOT NULL DEFAULT 120,
    status schedule_status DEFAULT 'scheduled',
    max_participants INTEGER,
    assignment_type VARCHAR(50) DEFAULT 'group', -- 'group' or 'individual'
    requirements JSONB DEFAULT '{}',
    resources JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT positive_duration CHECK (duration_minutes > 0),
    CONSTRAINT future_schedule CHECK (scheduled_date > created_at)
);

-- Schedule assignments table (links schedules to groups or individual students)
CREATE TABLE schedule_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    schedule_id UUID NOT NULL REFERENCES schedules(id) ON DELETE CASCADE,
    group_id UUID REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    assigned_seat INTEGER,
    assigned_computer INTEGER,
    status VARCHAR(50) DEFAULT 'assigned',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Either group_id or user_id must be set, but not both
    CONSTRAINT assignment_target CHECK (
        (group_id IS NOT NULL AND user_id IS NULL) OR
        (group_id IS NULL AND user_id IS NOT NULL)
    ),
    
    UNIQUE(schedule_id, group_id),
    UNIQUE(schedule_id, user_id),
    UNIQUE(schedule_id, assigned_seat),
    UNIQUE(schedule_id, assigned_computer)
);

-- Submissions table
CREATE TABLE submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    schedule_id UUID NOT NULL REFERENCES schedules(id) ON DELETE CASCADE,
    group_id UUID REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    submission_type submission_type NOT NULL DEFAULT 'mixed',
    file_paths JSONB DEFAULT '[]',
    text_content TEXT,
    metadata JSONB DEFAULT '{}',
    status submission_status DEFAULT 'pending',
    submitted_at TIMESTAMP WITH TIME ZONE,
    is_late BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Either group_id or user_id must be set, but not both
    CONSTRAINT submission_owner CHECK (
        (group_id IS NOT NULL AND user_id IS NULL) OR
        (group_id IS NULL AND user_id IS NOT NULL)
    ),
    
    UNIQUE(schedule_id, group_id),
    UNIQUE(schedule_id, user_id)
);

-- Grades table
CREATE TABLE grades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    submission_id UUID NOT NULL REFERENCES submissions(id) ON DELETE CASCADE,
    instructor_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL DEFAULT 100.00,
    feedback TEXT,
    rubric_data JSONB DEFAULT '{}',
    graded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_score CHECK (score >= 0 AND score <= max_score),
    CONSTRAINT positive_max_score CHECK (max_score > 0),
    
    UNIQUE(submission_id)
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_student_id ON users(student_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_classes_instructor ON classes(instructor_id);
CREATE INDEX idx_groups_class ON groups(class_id);
CREATE INDEX idx_groups_leader ON groups(leader_id);
CREATE INDEX idx_group_members_group ON group_members(group_id);
CREATE INDEX idx_group_members_user ON group_members(user_id);
CREATE INDEX idx_schedules_lab ON schedules(lab_id);
CREATE INDEX idx_schedules_instructor ON schedules(instructor_id);
CREATE INDEX idx_schedules_class ON schedules(class_id);
CREATE INDEX idx_schedules_date ON schedules(scheduled_date);
CREATE INDEX idx_schedule_assignments_schedule ON schedule_assignments(schedule_id);
CREATE INDEX idx_schedule_assignments_group ON schedule_assignments(group_id);
CREATE INDEX idx_schedule_assignments_user ON schedule_assignments(user_id);
CREATE INDEX idx_submissions_schedule ON submissions(schedule_id);
CREATE INDEX idx_submissions_group ON submissions(group_id);
CREATE INDEX idx_submissions_user ON submissions(user_id);
CREATE INDEX idx_submissions_status ON submissions(status);
CREATE INDEX idx_grades_submission ON grades(submission_id);
CREATE INDEX idx_grades_instructor ON grades(instructor_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_classes_updated_at BEFORE UPDATE ON classes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_labs_updated_at BEFORE UPDATE ON labs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_groups_updated_at BEFORE UPDATE ON groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_schedules_updated_at BEFORE UPDATE ON schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_submissions_updated_at BEFORE UPDATE ON submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_grades_updated_at BEFORE UPDATE ON grades FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data
INSERT INTO labs (name, total_computers, total_seats, location) VALUES
('Lab 1', 15, 50, 'Computer Science Building - Ground Floor'),
('Lab 2', 19, 50, 'Computer Science Building - First Floor');

-- Insert sample admin user (password: admin123)
INSERT INTO users (first_name, last_name, email, password_hash, role) VALUES
('Admin', 'User', '<EMAIL>', '$2b$10$gs5Aqyzhxh/xV66Vf9BiKOrrbuQxHFbY6bA1v77wmb6q0/7iWHwre', 'admin');

-- Insert sample instructor (password: instructor123)
INSERT INTO users (first_name, last_name, email, password_hash, role) VALUES
('John', 'Smith', '<EMAIL>', '$2b$10$7VaaPaMnnGCExmZN.dQZ2.yA7HShFLNRXvtMhvMdKjv1122g36wzy', 'instructor');

-- Insert sample classes
INSERT INTO classes (name, grade, stream, instructor_id) VALUES
('11th Non-Medical A', 11, 'Non-Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('11th Non-Medical B', 11, 'Non-Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('11th Non-Medical C', 11, 'Non-Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('11th Medical A', 11, 'Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('11th Medical B', 11, 'Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('11th Commerce A', 11, 'Commerce', (SELECT id FROM users WHERE email = '<EMAIL>')),
('12th Non-Medical A', 12, 'Non-Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('12th Non-Medical B', 12, 'Non-Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('12th Medical A', 12, 'Medical', (SELECT id FROM users WHERE email = '<EMAIL>')),
('12th Commerce A', 12, 'Commerce', (SELECT id FROM users WHERE email = '<EMAIL>'));

COMMIT;
