{"name": "labsyncpro", "version": "1.0.0", "description": "Laboratory Management System for Computer Labs", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "test": "cd client && npm test", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "test:all": "npm run test:client && npm run test:server"}, "keywords": ["laboratory", "management", "education", "scheduling"], "author": "LabSyncPro Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}