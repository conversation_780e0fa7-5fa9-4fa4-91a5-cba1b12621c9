const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticateToken, requireInstructor } = require('../middleware/auth');

const router = express.Router();

// Get lab computers with assignment status
router.get('/labs/:labId/computers', authenticateToken, async (req, res) => {
  try {
    const { labId } = req.params;
    const { scheduleId } = req.query;

    let computersQuery = `
      SELECT 
        c.id,
        c.computer_name,
        c.computer_number,
        c.specifications,
        c.is_functional,
        sa.id as assignment_id,
        sa.group_id,
        sa.user_id,
        g.name as group_name,
        u.first_name,
        u.last_name,
        u.student_id
      FROM computers c
      LEFT JOIN schedule_assignments sa ON c.computer_number = sa.assigned_computer
      LEFT JOIN groups g ON sa.group_id = g.id
      LEFT JOIN users u ON sa.user_id = u.id
      WHERE c.lab_id = $1
    `;

    const queryParams = [labId];

    if (scheduleId) {
      computersQuery += ` AND (sa.schedule_id = $2 OR sa.schedule_id IS NULL)`;
      queryParams.push(scheduleId);
    }

    computersQuery += ` ORDER BY c.computer_number`;

    const result = await query(computersQuery, queryParams);

    res.json(result.rows);
  } catch (error) {
    console.error('Get lab computers error:', error);
    res.status(500).json({ error: 'Failed to fetch lab computers' });
  }
});

// Get lab seats with assignment status
router.get('/labs/:labId/seats', authenticateToken, async (req, res) => {
  try {
    const { labId } = req.params;
    const { scheduleId } = req.query;

    let seatsQuery = `
      SELECT
        s.id,
        s.seat_number,
        s.is_available,
        sa.id as assignment_id,
        sa.user_id,
        sa.group_id,
        u.first_name,
        u.last_name,
        u.student_id,
        g.name as group_name
      FROM seats s
      LEFT JOIN schedule_assignments sa ON s.seat_number = sa.assigned_seat
      LEFT JOIN users u ON sa.user_id = u.id
      LEFT JOIN groups g ON sa.group_id = g.id
      WHERE s.lab_id = $1
    `;

    const queryParams = [labId];

    if (scheduleId) {
      seatsQuery += ` AND (sa.schedule_id = $2 OR sa.schedule_id IS NULL)`;
      queryParams.push(scheduleId);
    }

    seatsQuery += ` ORDER BY s.seat_number`;

    const result = await query(seatsQuery, queryParams);

    res.json(result.rows);
  } catch (error) {
    console.error('Get lab seats error:', error);
    res.status(500).json({ error: 'Failed to fetch lab seats' });
  }
});

// Get seat assignments for a lab
router.get('/labs/:labId/seat-assignments', authenticateToken, async (req, res) => {
  try {
    const { labId } = req.params;
    const { scheduleId } = req.query;

    let assignmentsQuery = `
      SELECT
        sa.id,
        sa.user_id,
        sa.group_id,
        sa.assigned_seat as seat_number,
        u.first_name,
        u.last_name,
        u.student_id,
        g.name as group_name,
        CONCAT(u.first_name, ' ', u.last_name) as student_name
      FROM schedule_assignments sa
      LEFT JOIN users u ON sa.user_id = u.id
      LEFT JOIN groups g ON sa.group_id = g.id
      JOIN schedules sch ON sa.schedule_id = sch.id
      WHERE sch.lab_id = $1 AND sa.assigned_seat IS NOT NULL
    `;

    const queryParams = [labId];

    if (scheduleId) {
      assignmentsQuery += ` AND sa.schedule_id = $2`;
      queryParams.push(scheduleId);
    }

    assignmentsQuery += ` ORDER BY s.seat_number`;

    const result = await query(assignmentsQuery, queryParams);

    res.json(result.rows);
  } catch (error) {
    console.error('Get seat assignments error:', error);
    res.status(500).json({ error: 'Failed to fetch seat assignments' });
  }
});

// Create seat assignment
router.post('/seat-assignments', [
  authenticateToken,
  requireInstructor,
  body('user_id').isUUID(),
  body('seat_number').isInt({ min: 1 }),
  body('schedule_id').isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { user_id, seat_number, schedule_id } = req.body;

    // Check if seat is already assigned for this schedule
    const existingAssignment = await query(
      'SELECT id FROM schedule_assignments WHERE assigned_seat = $1 AND schedule_id = $2',
      [seat_number, schedule_id]
    );

    if (existingAssignment.rows.length > 0) {
      return res.status(409).json({ error: 'Seat is already assigned for this schedule' });
    }

    // Check if user is already assigned a seat for this schedule
    const existingUserAssignment = await query(
      'SELECT id FROM schedule_assignments WHERE user_id = $1 AND schedule_id = $2',
      [user_id, schedule_id]
    );

    if (existingUserAssignment.rows.length > 0) {
      return res.status(409).json({ error: 'User is already assigned a seat for this schedule' });
    }

    // Create the assignment
    const result = await query(`
      INSERT INTO schedule_assignments (user_id, assigned_seat, schedule_id)
      VALUES ($1, $2, $3)
      RETURNING *
    `, [user_id, seat_number, schedule_id]);

    res.status(201).json({
      message: 'Seat assignment created successfully',
      assignment: result.rows[0]
    });
  } catch (error) {
    console.error('Create seat assignment error:', error);
    res.status(500).json({ error: 'Failed to create seat assignment' });
  }
});

// Update seat assignment
router.put('/seat-assignments/:id', [
  authenticateToken,
  requireInstructor,
  body('seat_id').optional().isUUID(),
  body('user_id').optional().isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { seat_id, user_id } = req.body;

    const updateFields = [];
    const values = [];
    let paramCount = 1;

    if (seat_id) {
      updateFields.push(`seat_id = $${paramCount}`);
      values.push(seat_id);
      paramCount++;
    }

    if (user_id) {
      updateFields.push(`user_id = $${paramCount}`);
      values.push(user_id);
      paramCount++;
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    values.push(id);

    const result = await query(`
      UPDATE seat_assignments 
      SET ${updateFields.join(', ')}, assigned_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Seat assignment not found' });
    }

    res.json({
      message: 'Seat assignment updated successfully',
      assignment: result.rows[0]
    });
  } catch (error) {
    console.error('Update seat assignment error:', error);
    res.status(500).json({ error: 'Failed to update seat assignment' });
  }
});

// Delete seat assignment
router.delete('/seat-assignments/:id', [authenticateToken, requireInstructor], async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      'DELETE FROM seat_assignments WHERE id = $1 RETURNING id',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Seat assignment not found' });
    }

    res.json({ message: 'Seat assignment deleted successfully' });
  } catch (error) {
    console.error('Delete seat assignment error:', error);
    res.status(500).json({ error: 'Failed to delete seat assignment' });
  }
});

// Assign group to computer
router.post('/computer-assignments', [
  authenticateToken,
  requireInstructor,
  body('schedule_id').isUUID(),
  body('computer_number').isInt({ min: 1 }),
  body('group_id').optional().isUUID(),
  body('user_id').optional().isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { schedule_id, computer_number, group_id, user_id } = req.body;

    // Validate that either group_id or user_id is provided, but not both
    if ((!group_id && !user_id) || (group_id && user_id)) {
      return res.status(400).json({
        error: 'Either group_id or user_id must be provided, but not both'
      });
    }

    // Check if computer is already assigned for this schedule
    const existingAssignment = await query(
      'SELECT id FROM schedule_assignments WHERE assigned_computer = $1 AND schedule_id = $2',
      [computer_number, schedule_id]
    );

    if (existingAssignment.rows.length > 0) {
      return res.status(409).json({ error: 'Computer is already assigned for this schedule' });
    }

    // Create the assignment
    const result = await query(`
      INSERT INTO schedule_assignments (schedule_id, assigned_computer, group_id, user_id)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [schedule_id, computer_number, group_id || null, user_id || null]);

    res.status(201).json({
      message: 'Computer assignment created successfully',
      assignment: result.rows[0]
    });
  } catch (error) {
    console.error('Create computer assignment error:', error);
    res.status(500).json({ error: 'Failed to create computer assignment' });
  }
});

module.exports = router;
