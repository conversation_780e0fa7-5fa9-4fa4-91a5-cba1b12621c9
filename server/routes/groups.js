const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticateToken, requireStudentOrInstructor } = require('../middleware/auth');

const router = express.Router();

// Get all groups (with optional filtering)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { classId, userId } = req.query;
    const currentUser = req.user;
    
    let whereClause = '';
    const queryParams = [];
    let paramCount = 1;

    // Students can only see groups they belong to or groups in their classes
    if (currentUser.role === 'student') {
      whereClause = `WHERE (
        EXISTS (SELECT 1 FROM group_members gm WHERE gm.group_id = g.id AND gm.user_id = $${paramCount})
        OR g.class_id IN (
          SELECT DISTINCT gr.class_id FROM groups gr 
          JOIN group_members gm2 ON gr.id = gm2.group_id 
          WHERE gm2.user_id = $${paramCount}
        )
      )`;
      queryParams.push(currentUser.id);
      paramCount++;
    }

    if (classId) {
      whereClause += whereClause ? ` AND g.class_id = $${paramCount}` : `WHERE g.class_id = $${paramCount}`;
      queryParams.push(classId);
      paramCount++;
    }

    if (userId) {
      whereClause += whereClause ? 
        ` AND EXISTS (SELECT 1 FROM group_members gm WHERE gm.group_id = g.id AND gm.user_id = $${paramCount})` :
        `WHERE EXISTS (SELECT 1 FROM group_members gm WHERE gm.group_id = g.id AND gm.user_id = $${paramCount})`;
      queryParams.push(userId);
      paramCount++;
    }

    const result = await query(`
      SELECT
        g.*,
        c.name as class_name,
        COUNT(gm.user_id) as member_count,
        leader.first_name as leader_first_name,
        leader.last_name as leader_last_name
      FROM groups g
      JOIN classes c ON g.class_id = c.id
      LEFT JOIN group_members gm ON g.id = gm.group_id
      LEFT JOIN users leader ON g.leader_id = leader.id
      ${whereClause}
      GROUP BY g.id, c.name, leader.first_name, leader.last_name
      ORDER BY g.created_at DESC
    `, queryParams);

    // Get members for each group
    const groupsWithMembers = await Promise.all(
      result.rows.map(async (group) => {
        const membersResult = await query(`
          SELECT
            u.id, u.first_name, u.last_name, u.student_id, u.email
          FROM group_members gm
          JOIN users u ON gm.user_id = u.id
          WHERE gm.group_id = $1
          ORDER BY u.first_name, u.last_name
        `, [group.id]);

        return {
          id: group.id,
          name: group.name,
          description: group.description,
          leaderId: group.leader_id,
          leaderName: group.leader_first_name && group.leader_last_name
            ? `${group.leader_first_name} ${group.leader_last_name}`
            : 'No Leader',
          classId: group.class_id,
          className: group.class_name,
          memberCount: parseInt(group.member_count),
          members: membersResult.rows.map(member => ({
            id: member.id,
            firstName: member.first_name,
            lastName: member.last_name,
            email: member.email,
            studentId: member.student_id
          })),
          createdAt: group.created_at
        };
      })
    );

    res.json(groupsWithMembers);
  } catch (error) {
    console.error('Get groups error:', error);
    res.status(500).json({ error: 'Failed to fetch groups' });
  }
});

// Get group by ID with members
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // Get group details
    const groupResult = await query(`
      SELECT 
        g.*,
        c.class_code, c.grade, c.stream, c.section,
        u.first_name as creator_first_name,
        u.last_name as creator_last_name
      FROM groups g
      JOIN classes c ON g.class_id = c.id
      LEFT JOIN users u ON g.created_by = u.id
      WHERE g.id = $1
    `, [id]);

    if (groupResult.rows.length === 0) {
      return res.status(404).json({ error: 'Group not found' });
    }

    const group = groupResult.rows[0];

    // Check if student has access to this group
    if (currentUser.role === 'student') {
      const membershipResult = await query(
        'SELECT 1 FROM group_members WHERE group_id = $1 AND user_id = $2',
        [id, currentUser.id]
      );

      if (membershipResult.rows.length === 0) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    // Get group members
    const membersResult = await query(`
      SELECT 
        u.id, u.first_name, u.last_name, u.student_id, u.email,
        gm.role, gm.joined_at
      FROM group_members gm
      JOIN users u ON gm.user_id = u.id
      WHERE gm.group_id = $1
      ORDER BY gm.role DESC, gm.joined_at
    `, [id]);

    // Get recent schedules for this group
    const schedulesResult = await query(`
      SELECT 
        s.id, s.title, s.scheduled_date, s.start_time, s.end_time, s.status,
        l.lab_name
      FROM schedules s
      JOIN labs l ON s.lab_id = l.id
      JOIN schedule_assignments sa ON s.id = sa.schedule_id
      WHERE sa.group_id = $1
      ORDER BY s.scheduled_date DESC, s.start_time DESC
      LIMIT 10
    `, [id]);

    res.json({
      group: {
        ...group,
        members: membersResult.rows,
        recentSchedules: schedulesResult.rows
      }
    });
  } catch (error) {
    console.error('Get group details error:', error);
    res.status(500).json({ error: 'Failed to fetch group details' });
  }
});

// Create new group
router.post('/', [
  authenticateToken,
  requireStudentOrInstructor,
  body('groupName').trim().isLength({ min: 1, max: 100 }),
  body('classId').isUUID(),
  body('maxMembers').optional().isInt({ min: 1, max: 10 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { groupName, classId, maxMembers = 4 } = req.body;
    const currentUser = req.user;

    // Check if class exists
    const classResult = await query(
      'SELECT id FROM classes WHERE id = $1',
      [classId]
    );

    if (classResult.rows.length === 0) {
      return res.status(404).json({ error: 'Class not found' });
    }

    // Check if group name already exists in this class
    const existingGroup = await query(
      'SELECT id FROM groups WHERE group_name = $1 AND class_id = $2',
      [groupName, classId]
    );

    if (existingGroup.rows.length > 0) {
      return res.status(409).json({ error: 'Group name already exists in this class' });
    }

    // Create group
    const result = await query(`
      INSERT INTO groups (group_name, class_id, max_members, created_by)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [groupName, classId, maxMembers, currentUser.id]);

    const group = result.rows[0];

    // Add creator as group leader
    await query(`
      INSERT INTO group_members (group_id, user_id, role)
      VALUES ($1, $2, 'leader')
    `, [group.id, currentUser.id]);

    res.status(201).json({
      message: 'Group created successfully',
      group
    });
  } catch (error) {
    console.error('Create group error:', error);
    res.status(500).json({ error: 'Failed to create group' });
  }
});

// Add member to group
router.post('/:id/members', [
  authenticateToken,
  body('userId').isUUID(),
  body('role').optional().isIn(['leader', 'member'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { userId, role = 'member' } = req.body;
    const currentUser = req.user;

    // Get group details
    const groupResult = await query(
      'SELECT * FROM groups WHERE id = $1',
      [id]
    );

    if (groupResult.rows.length === 0) {
      return res.status(404).json({ error: 'Group not found' });
    }

    const group = groupResult.rows[0];

    // Check if current user can add members (must be group leader or instructor)
    if (currentUser.role === 'student') {
      const leadershipResult = await query(
        'SELECT 1 FROM group_members WHERE group_id = $1 AND user_id = $2 AND role = $3',
        [id, currentUser.id, 'leader']
      );

      if (leadershipResult.rows.length === 0) {
        return res.status(403).json({ error: 'Only group leaders can add members' });
      }
    }

    // Check if user exists and is a student
    const userResult = await query(
      'SELECT id, role FROM users WHERE id = $1 AND is_active = true',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (userResult.rows[0].role !== 'student') {
      return res.status(400).json({ error: 'Only students can be added to groups' });
    }

    // Check if user is already a member
    const membershipResult = await query(
      'SELECT 1 FROM group_members WHERE group_id = $1 AND user_id = $2',
      [id, userId]
    );

    if (membershipResult.rows.length > 0) {
      return res.status(409).json({ error: 'User is already a member of this group' });
    }

    // Check group capacity
    const currentMembersResult = await query(
      'SELECT COUNT(*) as count FROM group_members WHERE group_id = $1',
      [id]
    );

    const currentMemberCount = parseInt(currentMembersResult.rows[0].count);
    if (currentMemberCount >= group.max_members) {
      return res.status(400).json({ error: 'Group is at maximum capacity' });
    }

    // Add member
    await query(`
      INSERT INTO group_members (group_id, user_id, role)
      VALUES ($1, $2, $3)
    `, [id, userId, role]);

    res.json({ message: 'Member added successfully' });
  } catch (error) {
    console.error('Add group member error:', error);
    res.status(500).json({ error: 'Failed to add member' });
  }
});

// Remove member from group
router.delete('/:id/members/:userId', authenticateToken, async (req, res) => {
  try {
    const { id, userId } = req.params;
    const currentUser = req.user;

    // Check if current user can remove members
    if (currentUser.role === 'student') {
      // Students can only remove themselves or if they are group leader
      if (currentUser.id !== userId) {
        const leadershipResult = await query(
          'SELECT 1 FROM group_members WHERE group_id = $1 AND user_id = $2 AND role = $3',
          [id, currentUser.id, 'leader']
        );

        if (leadershipResult.rows.length === 0) {
          return res.status(403).json({ error: 'Access denied' });
        }
      }
    }

    // Check if membership exists
    const membershipResult = await query(
      'SELECT role FROM group_members WHERE group_id = $1 AND user_id = $2',
      [id, userId]
    );

    if (membershipResult.rows.length === 0) {
      return res.status(404).json({ error: 'Membership not found' });
    }

    // Don't allow removing the last leader
    if (membershipResult.rows[0].role === 'leader') {
      const leaderCountResult = await query(
        'SELECT COUNT(*) as count FROM group_members WHERE group_id = $1 AND role = $2',
        [id, 'leader']
      );

      if (parseInt(leaderCountResult.rows[0].count) <= 1) {
        return res.status(400).json({ error: 'Cannot remove the last group leader' });
      }
    }

    // Remove member
    await query(
      'DELETE FROM group_members WHERE group_id = $1 AND user_id = $2',
      [id, userId]
    );

    res.json({ message: 'Member removed successfully' });
  } catch (error) {
    console.error('Remove group member error:', error);
    res.status(500).json({ error: 'Failed to remove member' });
  }
});

// Update group
router.put('/:id', [
  authenticateToken,
  body('groupName').optional().trim().isLength({ min: 1, max: 100 }),
  body('maxMembers').optional().isInt({ min: 1, max: 10 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { groupName, maxMembers } = req.body;
    const currentUser = req.user;

    // Check if current user can update group
    if (currentUser.role === 'student') {
      const leadershipResult = await query(
        'SELECT 1 FROM group_members WHERE group_id = $1 AND user_id = $2 AND role = $3',
        [id, currentUser.id, 'leader']
      );

      if (leadershipResult.rows.length === 0) {
        return res.status(403).json({ error: 'Only group leaders can update group details' });
      }
    }

    const updateFields = [];
    const values = [];
    let paramCount = 1;

    if (groupName) {
      updateFields.push(`group_name = $${paramCount}`);
      values.push(groupName);
      paramCount++;
    }

    if (maxMembers) {
      // Check if new max is not less than current member count
      const currentMembersResult = await query(
        'SELECT COUNT(*) as count FROM group_members WHERE group_id = $1',
        [id]
      );

      const currentMemberCount = parseInt(currentMembersResult.rows[0].count);
      if (maxMembers < currentMemberCount) {
        return res.status(400).json({ 
          error: `Cannot set max members to ${maxMembers}. Current member count is ${currentMemberCount}` 
        });
      }

      updateFields.push(`max_members = $${paramCount}`);
      values.push(maxMembers);
      paramCount++;
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    values.push(id);

    const result = await query(`
      UPDATE groups 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Group not found' });
    }

    res.json({
      message: 'Group updated successfully',
      group: result.rows[0]
    });
  } catch (error) {
    console.error('Update group error:', error);
    res.status(500).json({ error: 'Failed to update group' });
  }
});

// Delete group
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // Check if current user can delete group
    if (currentUser.role === 'student') {
      const leadershipResult = await query(
        'SELECT 1 FROM group_members WHERE group_id = $1 AND user_id = $2 AND role = $3',
        [id, currentUser.id, 'leader']
      );

      if (leadershipResult.rows.length === 0) {
        return res.status(403).json({ error: 'Only group leaders can delete groups' });
      }
    }

    // Check if group has any schedule assignments
    const assignmentsResult = await query(
      'SELECT COUNT(*) as count FROM schedule_assignments WHERE group_id = $1',
      [id]
    );

    if (parseInt(assignmentsResult.rows[0].count) > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete group with existing schedule assignments' 
      });
    }

    const result = await query(
      'DELETE FROM groups WHERE id = $1 RETURNING id',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Group not found' });
    }

    res.json({ message: 'Group deleted successfully' });
  } catch (error) {
    console.error('Delete group error:', error);
    res.status(500).json({ error: 'Failed to delete group' });
  }
});

// Get available students for a class (students not in any group)
router.get('/available-students/:classId', authenticateToken, async (req, res) => {
  try {
    const { classId } = req.params;

    const result = await query(`
      SELECT
        u.id,
        u.first_name,
        u.last_name,
        u.email,
        u.student_id
      FROM users u
      WHERE u.role = 'student'
        AND u.is_active = true
        AND u.id NOT IN (
          SELECT DISTINCT gm.user_id
          FROM group_members gm
          JOIN groups g ON gm.group_id = g.id
          WHERE g.class_id = $1
        )
      ORDER BY u.first_name, u.last_name
    `, [classId]);

    const students = result.rows.map(row => ({
      id: row.id,
      firstName: row.first_name,
      lastName: row.last_name,
      email: row.email,
      studentId: row.student_id
    }));

    res.json(students);
  } catch (error) {
    console.error('Get available students error:', error);
    res.status(500).json({ error: 'Failed to fetch available students' });
  }
});

module.exports = router;
