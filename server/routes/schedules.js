const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticateToken, requireInstructor, requireStudentOrInstructor } = require('../middleware/auth');

const router = express.Router();

// Get all schedules with filtering
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      labId, 
      classId, 
      instructorId, 
      date, 
      status, 
      page = 1, 
      limit = 20 
    } = req.query;
    const currentUser = req.user;
    
    let whereClause = '';
    const queryParams = [];
    let paramCount = 1;

    // Students can only see schedules they are assigned to
    if (currentUser.role === 'student') {
      whereClause = `WHERE (
        EXISTS (
          SELECT 1 FROM schedule_assignments sa 
          WHERE sa.schedule_id = s.id AND sa.user_id = $${paramCount}
        ) OR EXISTS (
          SELECT 1 FROM schedule_assignments sa 
          JOIN group_members gm ON sa.group_id = gm.group_id 
          WHERE sa.schedule_id = s.id AND gm.user_id = $${paramCount}
        )
      )`;
      queryParams.push(currentUser.id);
      paramCount++;
    }

    // Apply filters
    if (labId) {
      whereClause += whereClause ? ` AND s.lab_id = $${paramCount}` : `WHERE s.lab_id = $${paramCount}`;
      queryParams.push(labId);
      paramCount++;
    }

    if (classId) {
      whereClause += whereClause ? ` AND s.class_id = $${paramCount}` : `WHERE s.class_id = $${paramCount}`;
      queryParams.push(classId);
      paramCount++;
    }

    if (instructorId) {
      whereClause += whereClause ? ` AND s.instructor_id = $${paramCount}` : `WHERE s.instructor_id = $${paramCount}`;
      queryParams.push(instructorId);
      paramCount++;
    }

    if (date) {
      whereClause += whereClause ? ` AND s.scheduled_date = $${paramCount}` : `WHERE s.scheduled_date = $${paramCount}`;
      queryParams.push(date);
      paramCount++;
    }

    if (status) {
      whereClause += whereClause ? ` AND s.status = $${paramCount}` : `WHERE s.status = $${paramCount}`;
      queryParams.push(status);
      paramCount++;
    }

    // Pagination
    const offset = (page - 1) * limit;
    queryParams.push(limit, offset);

    const result = await query(`
      SELECT
        s.*,
        l.name as lab_name,
        c.name as class_name,
        u.first_name as instructor_first_name,
        u.last_name as instructor_last_name,
        COUNT(DISTINCT sa.id) as assignment_count,
        COUNT(DISTINCT sub.id) as submission_count
      FROM schedules s
      JOIN labs l ON s.lab_id = l.id
      LEFT JOIN classes c ON s.class_id = c.id
      JOIN users u ON s.instructor_id = u.id
      LEFT JOIN schedule_assignments sa ON s.id = sa.schedule_id
      LEFT JOIN submissions sub ON s.id = sub.schedule_id
      ${whereClause}
      GROUP BY s.id, l.name, c.name, u.first_name, u.last_name
      ORDER BY s.scheduled_date DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `, queryParams);

    // Get total count
    const countResult = await query(`
      SELECT COUNT(DISTINCT s.id) as total 
      FROM schedules s
      JOIN labs l ON s.lab_id = l.id
      LEFT JOIN classes c ON s.class_id = c.id
      JOIN users u ON s.instructor_id = u.id
      ${whereClause}
    `, queryParams.slice(0, -2));

    res.json({
      schedules: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        pages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  } catch (error) {
    console.error('Get schedules error:', error);
    res.status(500).json({ error: 'Failed to fetch schedules' });
  }
});

// Get schedule by ID with detailed information
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // Get schedule details
    const scheduleResult = await query(`
      SELECT
        s.*,
        l.name as lab_name, l.total_computers, l.total_seats,
        c.name as class_name, c.grade, c.stream,
        u.first_name as instructor_first_name,
        u.last_name as instructor_last_name,
        u.email as instructor_email
      FROM schedules s
      JOIN labs l ON s.lab_id = l.id
      LEFT JOIN classes c ON s.class_id = c.id
      JOIN users u ON s.instructor_id = u.id
      WHERE s.id = $1
    `, [id]);

    if (scheduleResult.rows.length === 0) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    const schedule = scheduleResult.rows[0];

    // Check if student has access to this schedule
    if (currentUser.role === 'student') {
      const accessResult = await query(`
        SELECT 1 FROM schedule_assignments sa 
        LEFT JOIN group_members gm ON sa.group_id = gm.group_id 
        WHERE sa.schedule_id = $1 AND (sa.user_id = $2 OR gm.user_id = $2)
      `, [id, currentUser.id]);

      if (accessResult.rows.length === 0) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    // Get assignments (groups and individual students)
    const assignmentsResult = await query(`
      SELECT 
        sa.id as assignment_id,
        sa.computer_id,
        c.computer_name,
        g.id as group_id,
        g.group_name,
        u.id as user_id,
        u.first_name,
        u.last_name,
        u.student_id
      FROM schedule_assignments sa
      LEFT JOIN computers c ON sa.computer_id = c.id
      LEFT JOIN groups g ON sa.group_id = g.id
      LEFT JOIN users u ON sa.user_id = u.id
      WHERE sa.schedule_id = $1
      ORDER BY c.computer_number, g.group_name, u.first_name
    `, [id]);

    // Get seat assignments
    const seatAssignmentsResult = await query(`
      SELECT 
        seat_a.id as assignment_id,
        seat_a.seat_id,
        s.seat_number,
        u.id as user_id,
        u.first_name,
        u.last_name,
        u.student_id
      FROM seat_assignments seat_a
      JOIN seats s ON seat_a.seat_id = s.id
      JOIN users u ON seat_a.user_id = u.id
      WHERE seat_a.schedule_id = $1
      ORDER BY s.seat_number
    `, [id]);

    // Get submissions
    const submissionsResult = await query(`
      SELECT 
        sub.id, sub.submission_type, sub.submitted_at, sub.is_late, sub.status,
        u.first_name, u.last_name, u.student_id,
        g.group_name,
        gr.score, gr.max_score, gr.graded_at
      FROM submissions sub
      JOIN users u ON sub.user_id = u.id
      LEFT JOIN groups g ON sub.group_id = g.id
      LEFT JOIN grades gr ON sub.id = gr.submission_id
      WHERE sub.schedule_id = $1
      ORDER BY sub.submitted_at DESC
    `, [id]);

    res.json({
      schedule: {
        ...schedule,
        assignments: assignmentsResult.rows,
        seatAssignments: seatAssignmentsResult.rows,
        submissions: submissionsResult.rows
      }
    });
  } catch (error) {
    console.error('Get schedule details error:', error);
    res.status(500).json({ error: 'Failed to fetch schedule details' });
  }
});

// Create new schedule (instructors only)
router.post('/', [
  authenticateToken,
  requireInstructor,
  body('title').trim().isLength({ min: 1, max: 200 }),
  body('labId').isUUID(),
  body('scheduledDate').isISO8601(),
  body('startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('endTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  body('classId').optional().isUUID(),
  body('maxParticipants').optional().isInt({ min: 1 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      title,
      description,
      labId,
      classId,
      scheduledDate,
      startTime,
      endTime,
      maxParticipants
    } = req.body;
    const currentUser = req.user;

    // Validate time range
    if (startTime >= endTime) {
      return res.status(400).json({ error: 'Start time must be before end time' });
    }

    // Check if lab exists
    const labResult = await query(
      'SELECT * FROM labs WHERE id = $1 AND is_active = true',
      [labId]
    );

    if (labResult.rows.length === 0) {
      return res.status(404).json({ error: 'Lab not found' });
    }

    // Check if class exists (if provided)
    if (classId) {
      const classResult = await query(
        'SELECT id FROM classes WHERE id = $1',
        [classId]
      );

      if (classResult.rows.length === 0) {
        return res.status(404).json({ error: 'Class not found' });
      }
    }

    // Check for scheduling conflicts
    const conflictResult = await query(`
      SELECT id, title FROM schedules 
      WHERE lab_id = $1 
      AND scheduled_date = $2
      AND status IN ('scheduled', 'in_progress')
      AND (
        (start_time <= $3 AND end_time > $3) OR
        (start_time < $4 AND end_time >= $4) OR
        (start_time >= $3 AND end_time <= $4)
      )
    `, [labId, scheduledDate, startTime, endTime]);

    if (conflictResult.rows.length > 0) {
      return res.status(409).json({ 
        error: 'Schedule conflict detected',
        conflictingSchedules: conflictResult.rows
      });
    }

    // Create schedule
    const result = await query(`
      INSERT INTO schedules (
        title, description, lab_id, instructor_id, class_id, 
        scheduled_date, start_time, end_time, max_participants
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [
      title, description, labId, currentUser.id, classId || null,
      scheduledDate, startTime, endTime, maxParticipants
    ]);

    res.status(201).json({
      message: 'Schedule created successfully',
      schedule: result.rows[0]
    });
  } catch (error) {
    console.error('Create schedule error:', error);
    res.status(500).json({ error: 'Failed to create schedule' });
  }
});

// Assign groups/students to schedule
router.post('/:id/assignments', [
  authenticateToken,
  requireInstructor,
  body('assignments').isArray(),
  body('assignments.*.type').isIn(['group', 'individual']),
  body('assignments.*.groupId').optional().isUUID(),
  body('assignments.*.userId').optional().isUUID(),
  body('assignments.*.computerId').optional().isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { assignments } = req.body;

    // Check if schedule exists and user owns it
    const scheduleResult = await query(
      'SELECT * FROM schedules WHERE id = $1 AND instructor_id = $2',
      [id, req.user.id]
    );

    if (scheduleResult.rows.length === 0) {
      return res.status(404).json({ error: 'Schedule not found or access denied' });
    }

    // Process assignments
    for (const assignment of assignments) {
      const { type, groupId, userId, computerId } = assignment;

      if (type === 'group' && groupId) {
        // Check if group exists
        const groupResult = await query(
          'SELECT id FROM groups WHERE id = $1',
          [groupId]
        );

        if (groupResult.rows.length === 0) {
          return res.status(400).json({ error: `Group ${groupId} not found` });
        }

        // Create assignment
        await query(`
          INSERT INTO schedule_assignments (schedule_id, group_id, computer_id)
          VALUES ($1, $2, $3)
          ON CONFLICT (schedule_id, group_id) DO UPDATE SET computer_id = $3
        `, [id, groupId, computerId || null]);

      } else if (type === 'individual' && userId) {
        // Check if user exists and is a student
        const userResult = await query(
          'SELECT id FROM users WHERE id = $1 AND role = $2 AND is_active = true',
          [userId, 'student']
        );

        if (userResult.rows.length === 0) {
          return res.status(400).json({ error: `Student ${userId} not found` });
        }

        // Create assignment
        await query(`
          INSERT INTO schedule_assignments (schedule_id, user_id, computer_id)
          VALUES ($1, $2, $3)
          ON CONFLICT (schedule_id, user_id) DO UPDATE SET computer_id = $3
        `, [id, userId, computerId || null]);
      }
    }

    res.json({ message: 'Assignments created successfully' });
  } catch (error) {
    console.error('Create assignments error:', error);
    res.status(500).json({ error: 'Failed to create assignments' });
  }
});

// Update schedule status
router.put('/:id/status', [
  authenticateToken,
  requireInstructor,
  body('status').isIn(['scheduled', 'in_progress', 'completed', 'cancelled'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { status } = req.body;

    const result = await query(`
      UPDATE schedules 
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2 AND instructor_id = $3
      RETURNING *
    `, [status, id, req.user.id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Schedule not found or access denied' });
    }

    res.json({
      message: 'Schedule status updated successfully',
      schedule: result.rows[0]
    });
  } catch (error) {
    console.error('Update schedule status error:', error);
    res.status(500).json({ error: 'Failed to update schedule status' });
  }
});

// Delete schedule (instructor only)
router.delete('/:id', [authenticateToken, requireInstructor], async (req, res) => {
  try {
    const { id } = req.params;

    // Check if schedule has submissions
    const submissionsResult = await query(
      'SELECT COUNT(*) as count FROM submissions WHERE schedule_id = $1',
      [id]
    );

    if (parseInt(submissionsResult.rows[0].count) > 0) {
      return res.status(400).json({
        error: 'Cannot delete schedule with existing submissions'
      });
    }

    const result = await query(
      'DELETE FROM schedules WHERE id = $1 AND instructor_id = $2 RETURNING id',
      [id, req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Schedule not found or access denied' });
    }

    res.json({ message: 'Schedule deleted successfully' });
  } catch (error) {
    console.error('Delete schedule error:', error);
    res.status(500).json({ error: 'Failed to delete schedule' });
  }
});

module.exports = router;
