const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

// Get all assignments for the current user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      classId, 
      labId, 
      type, 
      status, 
      page = 1, 
      limit = 20 
    } = req.query;
    const currentUser = req.user;
    
    let whereClause = '';
    const queryParams = [];
    let paramCount = 1;

    // Base query to get practical assignments (schedule_assignments) with schedule details
    let baseQuery = `
      SELECT
        sa.id,
        sa.schedule_id,
        s.title as schedule_title,
        s.description,
        s.scheduled_date,
        s.duration_minutes,
        s.status,
        s.assignment_type,
        l.name as lab_name,
        c.name as class_name,
        CONCAT(instructor.first_name, ' ', instructor.last_name) as instructor_name,
        sa.group_id,
        g.name as group_name,
        sa.user_id,
        CONCAT(student.first_name, ' ', student.last_name) as student_name,
        sa.assigned_computer,
        comp.computer_name,
        sa.assigned_seat,
        seat_a.seat_id,
        seats.seat_number,
        sa.created_at as assigned_at
      FROM schedule_assignments sa
      JOIN schedules s ON sa.schedule_id = s.id
      JOIN labs l ON s.lab_id = l.id
      LEFT JOIN classes c ON s.class_id = c.id
      JOIN users instructor ON s.instructor_id = instructor.id
      LEFT JOIN groups g ON sa.group_id = g.id
      LEFT JOIN users student ON sa.user_id = student.id
      LEFT JOIN computers comp ON sa.assigned_computer = comp.computer_number AND comp.lab_id = s.lab_id
      LEFT JOIN seat_assignments seat_a ON sa.schedule_id = seat_a.schedule_id AND sa.user_id = seat_a.user_id
      LEFT JOIN seats ON seat_a.seat_id = seats.id
    `;

    // Filter based on user role
    if (currentUser.role === 'student') {
      // Students see assignments they are directly assigned to or through groups
      whereClause = `WHERE (
        sa.user_id = $${paramCount} OR 
        EXISTS (
          SELECT 1 FROM group_members gm 
          WHERE gm.group_id = sa.group_id AND gm.user_id = $${paramCount}
        )
      )`;
      queryParams.push(currentUser.id);
      paramCount++;
    } else if (currentUser.role === 'instructor') {
      // Instructors see assignments for schedules they created
      whereClause = `WHERE s.instructor_id = $${paramCount}`;
      queryParams.push(currentUser.id);
      paramCount++;
    }
    // Admins see all assignments (no additional filter)

    // Add additional filters
    if (classId) {
      whereClause += whereClause ? ' AND' : 'WHERE';
      whereClause += ` s.class_id = $${paramCount}`;
      queryParams.push(classId);
      paramCount++;
    }

    if (labId) {
      whereClause += whereClause ? ' AND' : 'WHERE';
      whereClause += ` s.lab_id = $${paramCount}`;
      queryParams.push(labId);
      paramCount++;
    }

    if (type && type !== 'all') {
      whereClause += whereClause ? ' AND' : 'WHERE';
      whereClause += ` s.assignment_type = $${paramCount}`;
      queryParams.push(type);
      paramCount++;
    }

    if (status && status !== 'all') {
      whereClause += whereClause ? ' AND' : 'WHERE';
      whereClause += ` s.status = $${paramCount}`;
      queryParams.push(status);
      paramCount++;
    }

    // Add pagination
    const offset = (page - 1) * limit;
    queryParams.push(limit, offset);

    const fullQuery = `
      ${baseQuery}
      ${whereClause}
      ORDER BY s.scheduled_date DESC, s.created_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `;

    const result = await query(fullQuery, queryParams);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM schedule_assignments sa
      JOIN schedules s ON sa.schedule_id = s.id
      JOIN labs l ON s.lab_id = l.id
      LEFT JOIN classes c ON s.class_id = c.id
      ${whereClause}
    `;

    const countResult = await query(countQuery, queryParams.slice(0, -2));

    // Transform the data to match frontend interface
    const assignments = result.rows.map(row => ({
      id: row.id,
      scheduleId: row.schedule_id,
      scheduleTitle: row.schedule_title,
      description: row.description || '',
      labName: row.lab_name,
      className: row.class_name || 'No Class',
      instructorName: row.instructor_name,
      scheduledDate: row.scheduled_date,
      durationMinutes: row.duration_minutes,
      status: row.status,
      assignmentType: row.assignment_type,
      groupId: row.group_id,
      groupName: row.group_name,
      userId: row.user_id,
      studentName: row.student_name,
      computerId: row.computer_id,
      computerName: row.computer_name,
      assignedAt: row.assigned_at
    }));

    res.json({
      assignments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        pages: Math.ceil(countResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('Get assignments error:', error);
    res.status(500).json({ error: 'Failed to fetch assignments' });
  }
});

// Get assignment details by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    const result = await query(`
      SELECT 
        sa.*,
        s.title as schedule_title,
        s.description,
        s.scheduled_date,
        s.duration_minutes,
        s.status,
        s.assignment_type,
        l.name as lab_name,
        l.location as lab_location,
        c.name as class_name,
        CONCAT(instructor.first_name, ' ', instructor.last_name) as instructor_name,
        g.group_name,
        CONCAT(student.first_name, ' ', student.last_name) as student_name,
        comp.computer_name,
        comp.computer_number
      FROM schedule_assignments sa
      JOIN schedules s ON sa.schedule_id = s.id
      JOIN labs l ON s.lab_id = l.id
      LEFT JOIN classes c ON s.class_id = c.id
      JOIN users instructor ON s.instructor_id = instructor.id
      LEFT JOIN groups g ON sa.group_id = g.id
      LEFT JOIN users student ON sa.user_id = student.id
      LEFT JOIN computers comp ON sa.computer_id = comp.id
      WHERE sa.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Assignment not found' });
    }

    const assignment = result.rows[0];

    // Check access permissions
    if (currentUser.role === 'student') {
      const hasAccess = assignment.user_id === currentUser.id || 
        await query(`
          SELECT 1 FROM group_members 
          WHERE group_id = $1 AND user_id = $2
        `, [assignment.group_id, currentUser.id]);
      
      if (!hasAccess.rows.length && assignment.user_id !== currentUser.id) {
        return res.status(403).json({ error: 'Access denied' });
      }
    } else if (currentUser.role === 'instructor') {
      const scheduleCheck = await query(
        'SELECT instructor_id FROM schedules WHERE id = $1',
        [assignment.schedule_id]
      );
      
      if (scheduleCheck.rows[0]?.instructor_id !== currentUser.id) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    res.json({ assignment });

  } catch (error) {
    console.error('Get assignment details error:', error);
    res.status(500).json({ error: 'Failed to fetch assignment details' });
  }
});

// Update assignment (for instructors)
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { computerId, notes } = req.body;
    const currentUser = req.user;

    if (currentUser.role !== 'instructor' && currentUser.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Check if assignment exists and user has permission
    const assignmentCheck = await query(`
      SELECT sa.*, s.instructor_id 
      FROM schedule_assignments sa
      JOIN schedules s ON sa.schedule_id = s.id
      WHERE sa.id = $1
    `, [id]);

    if (assignmentCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Assignment not found' });
    }

    if (currentUser.role === 'instructor' && 
        assignmentCheck.rows[0].instructor_id !== currentUser.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Update assignment
    await query(`
      UPDATE schedule_assignments 
      SET computer_id = $1, notes = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [computerId || null, notes || null, id]);

    res.json({ message: 'Assignment updated successfully' });

  } catch (error) {
    console.error('Update assignment error:', error);
    res.status(500).json({ error: 'Failed to update assignment' });
  }
});

// Create new assignment
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      schedule_id,
      group_id,
      user_id,
      assigned_computer,
      assigned_seat,
      assignment_type
    } = req.body;
    const currentUser = req.user;

    // Check permissions
    if (currentUser.role !== 'instructor' && currentUser.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Validate that either group_id or user_id is provided, but not both
    if ((!group_id && !user_id) || (group_id && user_id)) {
      return res.status(400).json({
        error: 'Either group_id or user_id must be provided, but not both'
      });
    }

    // If schedule_id is not provided, we need to find or create a schedule
    let scheduleId = schedule_id;

    if (!scheduleId) {
      // For capacity planning assignments, we need to create a default schedule
      // or find an existing one for the current class/lab combination
      return res.status(400).json({
        error: 'schedule_id is required for assignments'
      });
    }

    // Check if schedule exists and user has permission
    const scheduleCheck = await query(`
      SELECT s.*, c.name as class_name, l.name as lab_name
      FROM schedules s
      LEFT JOIN classes c ON s.class_id = c.id
      LEFT JOIN labs l ON s.lab_id = l.id
      WHERE s.id = $1
    `, [scheduleId]);

    if (scheduleCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    const schedule = scheduleCheck.rows[0];

    // For instructors, check if they own the schedule
    if (currentUser.role === 'instructor' && schedule.instructor_id !== currentUser.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Check if computer is available (if assigned)
    if (assigned_computer) {
      const computerCheck = await query(`
        SELECT c.*,
               EXISTS(
                 SELECT 1 FROM schedule_assignments sa
                 WHERE sa.assigned_computer = c.id
                 AND sa.schedule_id = $1
               ) as is_assigned
        FROM computers c
        WHERE c.id = $2
      `, [scheduleId, assigned_computer]);

      if (computerCheck.rows.length === 0) {
        return res.status(404).json({ error: 'Computer not found' });
      }

      if (!computerCheck.rows[0].is_functional) {
        return res.status(400).json({ error: 'Computer is not functional' });
      }

      if (computerCheck.rows[0].is_assigned) {
        return res.status(409).json({ error: 'Computer is already assigned' });
      }
    }

    // Create the assignment
    const result = await query(`
      INSERT INTO schedule_assignments (
        schedule_id, group_id, user_id, assigned_computer, assigned_seat, status
      )
      VALUES ($1, $2, $3, $4, $5, 'assigned')
      RETURNING *
    `, [
      scheduleId,
      group_id || null,
      user_id || null,
      assigned_computer || null,
      assigned_seat || null
    ]);

    res.status(201).json({
      message: 'Assignment created successfully',
      assignment: result.rows[0]
    });

  } catch (error) {
    console.error('Create assignment error:', error);
    res.status(500).json({ error: 'Failed to create assignment' });
  }
});

module.exports = router;
