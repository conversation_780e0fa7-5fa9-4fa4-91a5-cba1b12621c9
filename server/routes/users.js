const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticateToken, requireInstructor, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all users (instructors and admins only)
router.get('/', [authenticateToken, requireInstructor], async (req, res) => {
  try {
    const { role, search, page = 1, limit = 20 } = req.query;
    
    let whereClause = 'WHERE is_active = true';
    const queryParams = [];
    let paramCount = 1;

    // Filter by role
    if (role && ['student', 'instructor', 'admin'].includes(role)) {
      whereClause += ` AND role = $${paramCount}`;
      queryParams.push(role);
      paramCount++;
    }

    // Search functionality
    if (search) {
      whereClause += ` AND (
        first_name ILIKE $${paramCount} OR 
        last_name ILIKE $${paramCount} OR 
        email ILIKE $${paramCount} OR 
        student_id ILIKE $${paramCount}
      )`;
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    // Get all users without pagination for the frontend table
    const result = await query(`
      SELECT
        id, email, first_name, last_name, role, student_id,
        is_active, created_at
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
    `, queryParams);

    // Format the response for the frontend
    const formattedUsers = result.rows.map(user => ({
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      role: user.role,
      studentId: user.student_id,
      isActive: user.is_active,
      createdAt: user.created_at
    }));

    res.json(formattedUsers);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get user by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // Students can only view their own profile, instructors can view all
    if (currentUser.role === 'student' && currentUser.id !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const result = await query(`
      SELECT 
        id, email, first_name, last_name, role, student_id, 
        is_active, created_at
      FROM users 
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = result.rows[0];

    // If it's a student, get additional information
    if (user.role === 'student') {
      // Get groups the student belongs to
      const groupsResult = await query(`
        SELECT 
          g.id, g.group_name, g.max_members,
          c.class_code, c.grade, c.stream, c.section,
          gm.role as group_role, gm.joined_at
        FROM group_members gm
        JOIN groups g ON gm.group_id = g.id
        JOIN classes c ON g.class_id = c.id
        WHERE gm.user_id = $1
        ORDER BY gm.joined_at DESC
      `, [id]);

      user.groups = groupsResult.rows;

      // Get recent submissions
      const submissionsResult = await query(`
        SELECT 
          s.id, s.submission_type, s.submitted_at, s.is_late, s.status,
          sch.title as schedule_title, sch.scheduled_date,
          g.score, g.max_score, g.graded_at
        FROM submissions s
        JOIN schedules sch ON s.schedule_id = sch.id
        LEFT JOIN grades g ON s.id = g.submission_id
        WHERE s.user_id = $1
        ORDER BY s.submitted_at DESC
        LIMIT 10
      `, [id]);

      user.recentSubmissions = submissionsResult.rows;
    }

    res.json({ user });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Create new user (admin only)
router.post('/', [
  authenticateToken,
  requireAdmin,
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  body('firstName').trim().isLength({ min: 1 }),
  body('lastName').trim().isLength({ min: 1 }),
  body('role').isIn(['student', 'instructor', 'admin']),
  body('studentId').optional().isLength({ min: 8, max: 8 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email, password, firstName, lastName, role, studentId } = req.body;

    // Check if user already exists
    const existingUser = await query(
      'SELECT id FROM users WHERE email = $1 OR ($2::text IS NOT NULL AND student_id = $2)',
      [email, studentId || null]
    );

    if (existingUser.rows.length > 0) {
      return res.status(409).json({ error: 'User already exists' });
    }

    // Hash password
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Insert new user
    const result = await query(`
      INSERT INTO users (email, password_hash, first_name, last_name, role, student_id)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, email, first_name, last_name, role, student_id, created_at
    `, [email, passwordHash, firstName, lastName, role, studentId || null]);

    const user = result.rows[0];

    res.status(201).json({
      message: 'User created successfully',
      user
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Update user
router.put('/:id', [
  authenticateToken,
  body('email').optional().isEmail().normalizeEmail(),
  body('firstName').optional().trim().isLength({ min: 1 }),
  body('lastName').optional().trim().isLength({ min: 1 }),
  body('studentId').optional().isLength({ min: 8, max: 8 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const currentUser = req.user;
    const { email, firstName, lastName, studentId, isActive } = req.body;

    // Students can only update their own profile
    // Instructors can update student profiles
    // Admins can update any profile
    if (currentUser.role === 'student' && currentUser.id !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Only admins can change isActive status
    if (isActive !== undefined && currentUser.role !== 'admin') {
      return res.status(403).json({ error: 'Only admins can change account status' });
    }

    const updateFields = [];
    const values = [];
    let paramCount = 1;

    if (email) {
      updateFields.push(`email = $${paramCount}`);
      values.push(email);
      paramCount++;
    }

    if (firstName) {
      updateFields.push(`first_name = $${paramCount}`);
      values.push(firstName);
      paramCount++;
    }

    if (lastName) {
      updateFields.push(`last_name = $${paramCount}`);
      values.push(lastName);
      paramCount++;
    }

    if (studentId) {
      updateFields.push(`student_id = $${paramCount}`);
      values.push(studentId);
      paramCount++;
    }

    if (isActive !== undefined && currentUser.role === 'admin') {
      updateFields.push(`is_active = $${paramCount}`);
      values.push(isActive);
      paramCount++;
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    values.push(id);

    const result = await query(`
      UPDATE users 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING id, email, first_name, last_name, role, student_id, is_active, updated_at
    `, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: 'User updated successfully',
      user: result.rows[0]
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Delete user (admin only)
router.delete('/:id', [authenticateToken, requireAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    // Soft delete by setting is_active to false
    const result = await query(
      'UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING id',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ message: 'User deactivated successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: 'Failed to deactivate user' });
  }
});

// Get students for group assignment
router.get('/students/available', authenticateToken, async (req, res) => {
  try {
    const { classId, excludeGroupId } = req.query;

    let whereClause = "WHERE u.role = 'student' AND u.is_active = true";
    const queryParams = [];
    let paramCount = 1;

    // Filter by class if provided
    if (classId) {
      whereClause += ` AND EXISTS (
        SELECT 1 FROM group_members gm 
        JOIN groups g ON gm.group_id = g.id 
        WHERE gm.user_id = u.id AND g.class_id = $${paramCount}
      )`;
      queryParams.push(classId);
      paramCount++;
    }

    // Exclude students already in a specific group
    if (excludeGroupId) {
      whereClause += ` AND NOT EXISTS (
        SELECT 1 FROM group_members gm 
        WHERE gm.user_id = u.id AND gm.group_id = $${paramCount}
      )`;
      queryParams.push(excludeGroupId);
      paramCount++;
    }

    const result = await query(`
      SELECT 
        u.id, u.first_name, u.last_name, u.student_id, u.email,
        COUNT(gm.id) as group_count
      FROM users u
      LEFT JOIN group_members gm ON u.id = gm.user_id
      ${whereClause}
      GROUP BY u.id, u.first_name, u.last_name, u.student_id, u.email
      ORDER BY u.first_name, u.last_name
    `, queryParams);

    res.json({
      students: result.rows
    });
  } catch (error) {
    console.error('Get available students error:', error);
    res.status(500).json({ error: 'Failed to fetch available students' });
  }
});

module.exports = router;
